<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛒 E-commerce VQA Chatbot</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 90vh;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .chat-header p {
            opacity: 0.9;
            font-size: 14px;
        }

        .status-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 12px;
            height: 12px;
            background: #4CAF50;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 20px;
            display: flex;
            align-items: flex-start;
        }

        .message.user {
            justify-content: flex-end;
        }

        .message-content {
            max-width: 70%;
            padding: 15px 20px;
            border-radius: 20px;
            position: relative;
        }

        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 5px;
        }

        .message.bot .message-content {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .message-image {
            max-width: 200px;
            border-radius: 10px;
            margin-top: 10px;
        }

        .message-meta {
            font-size: 12px;
            opacity: 0.7;
            margin-top: 5px;
        }

        .suggested-questions {
            margin-top: 10px;
        }

        .suggested-question {
            display: inline-block;
            background: #f0f0f0;
            padding: 5px 10px;
            margin: 2px;
            border-radius: 15px;
            font-size: 12px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .suggested-question:hover {
            background: #e0e0e0;
        }

        .chat-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: flex-end;
        }

        .input-group {
            flex: 1;
            position: relative;
        }

        .input-field {
            width: 100%;
            padding: 15px 50px 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
            resize: none;
            min-height: 50px;
            max-height: 100px;
        }

        .input-field:focus {
            border-color: #667eea;
        }

        .image-upload {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            cursor: pointer;
            color: #667eea;
            font-size: 20px;
        }

        .send-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            font-size: 20px;
            transition: transform 0.3s;
        }

        .send-button:hover {
            transform: scale(1.1);
        }

        .send-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .typing-indicator {
            display: none;
            padding: 15px 20px;
            background: white;
            border-radius: 20px;
            border-bottom-left-radius: 5px;
            max-width: 70%;
            border: 1px solid #e0e0e0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .typing-dots {
            display: flex;
            gap: 5px;
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: #667eea;
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }

        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }

        .metrics-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            background: white;
            padding: 15px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            font-size: 12px;
            min-width: 200px;
            z-index: 1000;
        }

        .metric-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .hidden {
            display: none !important;
        }

        @media (max-width: 768px) {
            .chat-container {
                width: 100%;
                height: 100vh;
                border-radius: 0;
            }
            
            .metrics-panel {
                position: relative;
                top: auto;
                right: auto;
                margin: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <div class="status-indicator"></div>
            <h1>🛒 E-commerce VQA Chatbot</h1>
            <p>Hỏi tôi về bất kỳ sản phẩm nào bằng hình ảnh!</p>
        </div>

        <div class="chat-messages" id="chatMessages">
            <div class="message bot">
                <div class="message-content">
                    <div>Xin chào! 👋 Tôi là trợ lý AI của shop. Bạn có thể gửi hình ảnh sản phẩm và hỏi tôi bất cứ điều gì về nó!</div>
                    <div class="suggested-questions">
                        <span class="suggested-question" onclick="sendSuggestedQuestion('Sản phẩm này có màu nào khác không?')">Có màu nào khác không?</span>
                        <span class="suggested-question" onclick="sendSuggestedQuestion('Giá của sản phẩm này là bao nhiêu?')">Giá bao nhiêu?</span>
                        <span class="suggested-question" onclick="sendSuggestedQuestion('Chất liệu của sản phẩm là gì?')">Chất liệu gì?</span>
                    </div>
                </div>
            </div>
            
            <div class="typing-indicator" id="typingIndicator">
                <div class="typing-dots">
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                    <div class="typing-dot"></div>
                </div>
            </div>
        </div>

        <div class="chat-input">
            <div class="input-container">
                <div class="input-group">
                    <textarea 
                        class="input-field" 
                        id="messageInput" 
                        placeholder="Nhập câu hỏi về sản phẩm..."
                        rows="1"
                    ></textarea>
                    <label class="image-upload" for="imageInput">📷</label>
                    <input type="file" id="imageInput" accept="image/*" style="display: none;">
                </div>
                <button class="send-button" id="sendButton" onclick="sendMessage()">➤</button>
            </div>
        </div>
    </div>

    <!-- Metrics Panel -->
    <div class="metrics-panel" id="metricsPanel">
        <h4>📊 Metrics</h4>
        <div class="metric-item">
            <span>Messages:</span>
            <span id="messageCount">0</span>
        </div>
        <div class="metric-item">
            <span>Avg Response:</span>
            <span id="avgResponseTime">0ms</span>
        </div>
        <div class="metric-item">
            <span>Avg Confidence:</span>
            <span id="avgConfidence">0%</span>
        </div>
        <div class="metric-item">
            <span>Session ID:</span>
            <span id="sessionId">-</span>
        </div>
    </div>

    <script>
        let sessionId = null;
        let messageCount = 0;
        let totalResponseTime = 0;
        let totalConfidence = 0;
        let selectedImage = null;

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-resize textarea
            const messageInput = document.getElementById('messageInput');
            messageInput.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = Math.min(this.scrollHeight, 100) + 'px';
            });

            // Enter key to send
            messageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });

            // Image upload
            document.getElementById('imageInput').addEventListener('change', function(e) {
                const file = e.target.files[0];
                if (file) {
                    selectedImage = file;
                    // Show preview (optional)
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        // Could show image preview here
                        console.log('Image selected:', file.name);
                    };
                    reader.readAsDataURL(file);
                }
            });
        });

        async function sendMessage() {
            const messageInput = document.getElementById('messageInput');
            const message = messageInput.value.trim();
            
            if (!message && !selectedImage) return;

            // Disable send button
            const sendButton = document.getElementById('sendButton');
            sendButton.disabled = true;

            // Add user message to chat
            addMessage('user', message, selectedImage);

            // Clear input
            messageInput.value = '';
            messageInput.style.height = 'auto';

            // Show typing indicator
            showTypingIndicator();

            try {
                // Prepare form data
                const formData = new FormData();
                formData.append('question', message || 'Mô tả hình ảnh này');
                if (sessionId) {
                    formData.append('session_id', sessionId);
                }
                if (selectedImage) {
                    formData.append('image', selectedImage);
                }

                // Send request
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                // Update session ID
                if (!sessionId) {
                    sessionId = data.session_id;
                    document.getElementById('sessionId').textContent = sessionId.substring(0, 8) + '...';
                }

                // Add bot response
                addMessage('bot', data.answer, null, {
                    responseTime: data.response_time,
                    confidence: data.confidence_score,
                    suggestions: data.suggested_questions
                });

                // Update metrics
                updateMetrics(data.response_time, data.confidence_score);

            } catch (error) {
                console.error('Error:', error);
                addMessage('bot', 'Xin lỗi, có lỗi xảy ra. Vui lòng thử lại sau.', null, {
                    responseTime: 0,
                    confidence: 0,
                    suggestions: []
                });
            } finally {
                // Hide typing indicator
                hideTypingIndicator();
                
                // Re-enable send button
                sendButton.disabled = false;
                
                // Clear selected image
                selectedImage = null;
                document.getElementById('imageInput').value = '';
            }
        }

        function addMessage(sender, text, image, metadata = {}) {
            const chatMessages = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;

            let imageHtml = '';
            if (image) {
                const imageUrl = URL.createObjectURL(image);
                imageHtml = `<img src="${imageUrl}" class="message-image" alt="Uploaded image">`;
            }

            let metaHtml = '';
            if (metadata.responseTime !== undefined) {
                metaHtml = `
                    <div class="message-meta">
                        ⏱️ ${(metadata.responseTime * 1000).toFixed(0)}ms | 
                        🎯 ${(metadata.confidence * 100).toFixed(0)}% confidence
                    </div>
                `;
            }

            let suggestionsHtml = '';
            if (metadata.suggestions && metadata.suggestions.length > 0) {
                suggestionsHtml = `
                    <div class="suggested-questions">
                        ${metadata.suggestions.map(q => 
                            `<span class="suggested-question" onclick="sendSuggestedQuestion('${q}')">${q}</span>`
                        ).join('')}
                    </div>
                `;
            }

            messageDiv.innerHTML = `
                <div class="message-content">
                    <div>${text}</div>
                    ${imageHtml}
                    ${metaHtml}
                    ${suggestionsHtml}
                </div>
            `;

            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function sendSuggestedQuestion(question) {
            document.getElementById('messageInput').value = question;
            sendMessage();
        }

        function showTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'block';
            const chatMessages = document.getElementById('chatMessages');
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        function hideTypingIndicator() {
            document.getElementById('typingIndicator').style.display = 'none';
        }

        function updateMetrics(responseTime, confidence) {
            messageCount++;
            totalResponseTime += responseTime;
            totalConfidence += confidence;

            document.getElementById('messageCount').textContent = messageCount;
            document.getElementById('avgResponseTime').textContent = 
                Math.round((totalResponseTime / messageCount) * 1000) + 'ms';
            document.getElementById('avgConfidence').textContent = 
                Math.round((totalConfidence / messageCount) * 100) + '%';
        }

        // Toggle metrics panel on mobile
        function toggleMetrics() {
            const panel = document.getElementById('metricsPanel');
            panel.classList.toggle('hidden');
        }
    </script>
</body>
</html>
