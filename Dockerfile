FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements
COPY requirements_ecommerce.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements_ecommerce.txt

# Copy application files
COPY . .

# Create database directory
RUN mkdir -p /app/data

# Expose port
EXPOSE 8000

# Start command
CMD ["python", "ecommerce_vqa_backend.py"]
