"""
Demo script fine-tuning với model nhỏ hơn để test
"""

from datasets import Dataset
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    Trainer,
    TrainingArguments,
    DataCollatorForLanguageModeling
)
from peft import LoraConfig, get_peft_model
import torch
from huggingface_hub import login
import os
import shutil
from PIL import Image
import numpy as np

# Đăng nhập HuggingFace
print("🚀 Demo Fine-tuning với model nhỏ")
login(token="*************************************")

# Tạo thư mục cache
cache_dir = "D:/THUC TAP/Finr_Tunning_Vintern/demo_cache"
os.makedirs(cache_dir, exist_ok=True)
os.environ["HF_DATASETS_CACHE"] = cache_dir
os.environ["HF_HOME"] = cache_dir

# 1. Tạo dataset demo text-only (không có ảnh để đơn giản)
print("📥 Tạo dataset demo...")

demo_data = []
questions = [
    "Xin chào, bạn có khỏe không?",
    "Hôm nay thời tiết thế nào?",
    "Bạn có thể giúp tôi không?",
    "Cảm ơn bạn rất nhiều",
    "Tạm biệt và hẹn gặp lại"
]

answers = [
    "Xin chào! Tôi khỏe, cảm ơn bạn đã hỏi.",
    "Hôm nay thời tiết rất đẹp và nắng.",
    "Tất nhiên! Tôi sẵn sàng giúp đỡ bạn.",
    "Không có gì, tôi rất vui được giúp bạn.",
    "Tạm biệt! Hẹn gặp lại bạn sớm nhé."
]

for i in range(20):  # 20 samples nhỏ
    question = questions[i % len(questions)]
    answer = answers[i % len(answers)]
    
    # Format theo kiểu chat
    text = f"<|im_start|>user\n{question}<|im_end|>\n<|im_start|>assistant\n{answer}<|im_end|>"
    demo_data.append({"text": text})

# Tạo dataset
train_dataset = Dataset.from_list(demo_data[:16])  # 16 cho train
test_dataset = Dataset.from_list(demo_data[16:])   # 4 cho test

print(f"✅ Đã tạo {len(train_dataset)} samples train và {len(test_dataset)} samples test")

# 2. Load model nhỏ hơn (GPT-2 Vietnamese)
print("🤖 Đang tải model nhỏ...")
try:
    model_name = "microsoft/DialoGPT-small"  # Model nhỏ để test
    
    tokenizer = AutoTokenizer.from_pretrained(
        model_name,
        cache_dir=cache_dir
    )
    
    # Thêm pad token
    if tokenizer.pad_token is None:
        tokenizer.pad_token = tokenizer.eos_token
    
    model = AutoModelForCausalLM.from_pretrained(
        model_name,
        torch_dtype=torch.float32,  # Sử dụng float32 cho CPU
        cache_dir=cache_dir
    )
    
    print("✅ Đã tải model thành công")
    
except Exception as e:
    print(f"❌ Lỗi khi tải model: {e}")
    exit(1)

# 3. Tokenize dataset
def tokenize_function(examples):
    return tokenizer(
        examples["text"],
        truncation=True,
        padding="max_length",
        max_length=128,  # Giảm độ dài để tiết kiệm bộ nhớ
        return_tensors="pt"
    )

print("🔄 Đang tokenize dataset...")
train_dataset = train_dataset.map(tokenize_function, batched=True)
test_dataset = test_dataset.map(tokenize_function, batched=True)

# 4. LoRA config đơn giản
print("⚙️ Cấu hình LoRA...")
lora_config = LoraConfig(
    r=4,  # Rank nhỏ
    lora_alpha=8,
    target_modules=["c_attn"],  # Chỉ target một module
    lora_dropout=0.1,
    bias="none",
    task_type="CAUSAL_LM"
)

model = get_peft_model(model, lora_config)
model.print_trainable_parameters()

# 5. Training arguments tối ưu
print("📋 Cấu hình training...")
training_args = TrainingArguments(
    output_dir="./demo-finetuned-model",
    per_device_train_batch_size=1,
    gradient_accumulation_steps=2,
    learning_rate=5e-4,
    num_train_epochs=1,  # Chỉ 1 epoch để test
    logging_steps=2,
    save_total_limit=1,
    save_steps=10,
    save_strategy="steps",
    load_best_model_at_end=False,
    remove_unused_columns=False,
    report_to=None,
    dataloader_num_workers=0,  # Tắt multiprocessing
)

# 6. Data collator
data_collator = DataCollatorForLanguageModeling(
    tokenizer=tokenizer,
    mlm=False,  # Causal LM
)

# 7. Trainer
print("🏋️ Khởi tạo Trainer...")
trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=train_dataset,
    data_collator=data_collator,
    tokenizer=tokenizer,
)

# 8. Fine-tune
print("🚀 Bắt đầu fine-tuning...")
try:
    trainer.train()
    print("✅ Fine-tuning hoàn thành!")
    
    # Lưu model
    output_dir = "./demo-finetuned-model"
    trainer.save_model(output_dir)
    tokenizer.save_pretrained(output_dir)
    print(f"💾 Đã lưu model tại: {output_dir}")
    
except Exception as e:
    print(f"❌ Lỗi trong quá trình training: {e}")

# 9. Test inference
print("🔍 Test inference...")
try:
    model.eval()
    test_text = "<|im_start|>user\nBạn có khỏe không?<|im_end|>\n<|im_start|>assistant\n"
    
    inputs = tokenizer(test_text, return_tensors="pt")
    
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=20,
            do_sample=True,
            temperature=0.7,
            pad_token_id=tokenizer.pad_token_id
        )
    
    generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
    print(f"Input: {test_text}")
    print(f"Generated: {generated_text}")
    
except Exception as e:
    print(f"❌ Lỗi khi test: {e}")

# 10. Dọn dẹp
print("🧹 Dọn dẹp...")
try:
    if os.path.exists(cache_dir):
        shutil.rmtree(cache_dir, ignore_errors=True)
    print("✅ Hoàn thành demo!")
    
except Exception as e:
    print(f"⚠️ Lỗi khi dọn dẹp: {e}")

print("\n🎉 Demo fine-tuning hoàn thành!")
print("💡 Đây là demo đơn giản với model nhỏ")
print("💡 Để fine-tune Vintern thật, cần GPU mạnh hơn và nhiều RAM hơn")
