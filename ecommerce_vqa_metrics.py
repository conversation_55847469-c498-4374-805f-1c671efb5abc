"""
Comprehensive Evaluation Metrics for E-commerce VQA Chatbot
<PERSON><PERSON>LEU, ROUGE, Semantic Similarity, và các metrics đặc thù cho e-commerce
"""

import sqlite3
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import json
import re
from typing import List, Dict, Tuple
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter
import nltk
from nltk.translate.bleu_score import sentence_bleu, SmoothingFunction
from rouge_score import rouge_scorer
from sentence_transformers import SentenceTransformer
from sklearn.metrics.pairwise import cosine_similarity
import warnings
warnings.filterwarnings('ignore')

# Download required NLTK data
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')

class EcommerceVQAMetrics:
    def __init__(self, db_path="ecommerce_vqa.db"):
        self.db_path = db_path
        self.rouge_scorer = rouge_scorer.RougeScorer(['rouge1', 'rouge2', 'rougeL'], use_stemmer=True)
        
        # Load sentence transformer for semantic similarity
        try:
            self.sentence_model = SentenceTransformer('paraphrase-multilingual-MiniLM-L12-v2')
        except:
            print("Warning: Could not load sentence transformer model")
            self.sentence_model = None
        
        # E-commerce specific keywords
        self.ecommerce_keywords = {
            'product_info': ['sản phẩm', 'hàng hóa', 'mặt hàng', 'item'],
            'price': ['giá', 'tiền', 'cost', 'price', 'phí', 'chi phí'],
            'color': ['màu', 'color', 'màu sắc'],
            'size': ['kích thước', 'size', 'cỡ', 'dimension'],
            'material': ['chất liệu', 'material', 'vật liệu'],
            'availability': ['còn hàng', 'available', 'inventory', 'stock'],
            'quality': ['chất lượng', 'quality', 'tốt', 'xấu'],
            'brand': ['thương hiệu', 'brand', 'hãng']
        }
    
    def connect_db(self):
        """Connect to database"""
        return sqlite3.connect(self.db_path)
    
    def calculate_bleu_score(self, reference: str, candidate: str) -> float:
        """Calculate BLEU score between reference and candidate"""
        try:
            # Tokenize
            reference_tokens = nltk.word_tokenize(reference.lower())
            candidate_tokens = nltk.word_tokenize(candidate.lower())
            
            # Calculate BLEU with smoothing
            smoothing = SmoothingFunction().method1
            score = sentence_bleu([reference_tokens], candidate_tokens, 
                                smoothing_function=smoothing)
            return score
        except:
            return 0.0
    
    def calculate_rouge_scores(self, reference: str, candidate: str) -> Dict[str, float]:
        """Calculate ROUGE scores"""
        try:
            scores = self.rouge_scorer.score(reference, candidate)
            return {
                'rouge1': scores['rouge1'].fmeasure,
                'rouge2': scores['rouge2'].fmeasure,
                'rougeL': scores['rougeL'].fmeasure
            }
        except:
            return {'rouge1': 0.0, 'rouge2': 0.0, 'rougeL': 0.0}
    
    def calculate_semantic_similarity(self, text1: str, text2: str) -> float:
        """Calculate semantic similarity using sentence transformers"""
        if not self.sentence_model:
            return 0.0
        
        try:
            embeddings = self.sentence_model.encode([text1, text2])
            similarity = cosine_similarity([embeddings[0]], [embeddings[1]])[0][0]
            return float(similarity)
        except:
            return 0.0
    
    def calculate_keyword_coverage(self, text: str, category: str = None) -> Dict[str, float]:
        """Calculate coverage of e-commerce keywords"""
        text_lower = text.lower()
        coverage = {}
        
        categories = [category] if category else self.ecommerce_keywords.keys()
        
        for cat in categories:
            if cat in self.ecommerce_keywords:
                keywords = self.ecommerce_keywords[cat]
                found_keywords = sum(1 for keyword in keywords if keyword in text_lower)
                coverage[cat] = found_keywords / len(keywords)
        
        return coverage
    
    def analyze_response_quality(self, user_question: str, bot_response: str) -> Dict[str, float]:
        """Comprehensive response quality analysis"""
        metrics = {}
        
        # Basic metrics
        metrics['response_length'] = len(bot_response.split())
        metrics['question_length'] = len(user_question.split())
        metrics['length_ratio'] = metrics['response_length'] / max(metrics['question_length'], 1)
        
        # Keyword coverage
        keyword_coverage = self.calculate_keyword_coverage(bot_response)
        metrics.update({f'keyword_{k}': v for k, v in keyword_coverage.items()})
        
        # Semantic similarity between question and answer
        metrics['semantic_similarity'] = self.calculate_semantic_similarity(user_question, bot_response)
        
        # Response completeness (heuristic)
        completeness_indicators = ['có', 'không', 'là', 'được', 'sẽ', 'có thể']
        found_indicators = sum(1 for indicator in completeness_indicators if indicator in bot_response.lower())
        metrics['completeness_score'] = min(found_indicators / len(completeness_indicators), 1.0)
        
        # Politeness score (heuristic)
        polite_words = ['xin chào', 'cảm ơn', 'vui lòng', 'xin lỗi', 'ạ', 'em', 'anh', 'chị']
        found_polite = sum(1 for word in polite_words if word in bot_response.lower())
        metrics['politeness_score'] = min(found_polite / 3, 1.0)
        
        return metrics
    
    def get_conversation_metrics(self, session_id: str = None, days: int = 7) -> Dict:
        """Get comprehensive conversation metrics"""
        conn = self.connect_db()
        
        # Build query
        where_clause = "WHERE timestamp >= datetime('now', '-{} days')".format(days)
        if session_id:
            where_clause += f" AND session_id = '{session_id}'"
        
        query = f"""
            SELECT session_id, user_message, bot_response, response_time, 
                   confidence_score, timestamp
            FROM messages 
            {where_clause}
            ORDER BY timestamp DESC
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        if df.empty:
            return {"error": "No data found"}
        
        # Calculate metrics
        metrics = {
            'total_conversations': len(df),
            'unique_sessions': df['session_id'].nunique(),
            'avg_response_time': df['response_time'].mean(),
            'avg_confidence': df['confidence_score'].mean(),
            'response_time_std': df['response_time'].std(),
            'confidence_std': df['confidence_score'].std()
        }
        
        # Quality metrics for each conversation
        quality_scores = []
        bleu_scores = []
        rouge_scores = []
        semantic_scores = []
        
        for _, row in df.iterrows():
            # Quality analysis
            quality = self.analyze_response_quality(row['user_message'], row['bot_response'])
            quality_scores.append(quality)
            
            # BLEU score (using question as reference - not ideal but for demonstration)
            bleu = self.calculate_bleu_score(row['user_message'], row['bot_response'])
            bleu_scores.append(bleu)
            
            # ROUGE scores
            rouge = self.calculate_rouge_scores(row['user_message'], row['bot_response'])
            rouge_scores.append(rouge)
            
            # Semantic similarity
            semantic = self.calculate_semantic_similarity(row['user_message'], row['bot_response'])
            semantic_scores.append(semantic)
        
        # Aggregate quality metrics
        if quality_scores:
            quality_df = pd.DataFrame(quality_scores)
            for col in quality_df.columns:
                metrics[f'avg_{col}'] = quality_df[col].mean()
        
        # Aggregate NLP metrics
        metrics['avg_bleu_score'] = np.mean(bleu_scores) if bleu_scores else 0
        metrics['avg_rouge1'] = np.mean([r['rouge1'] for r in rouge_scores]) if rouge_scores else 0
        metrics['avg_rouge2'] = np.mean([r['rouge2'] for r in rouge_scores]) if rouge_scores else 0
        metrics['avg_rougeL'] = np.mean([r['rougeL'] for r in rouge_scores]) if rouge_scores else 0
        metrics['avg_semantic_similarity'] = np.mean(semantic_scores) if semantic_scores else 0
        
        return metrics
    
    def generate_performance_report(self, days: int = 7) -> Dict:
        """Generate comprehensive performance report"""
        print("📊 Generating E-commerce VQA Performance Report...")
        
        # Get overall metrics
        overall_metrics = self.get_conversation_metrics(days=days)
        
        # Get daily breakdown
        conn = self.connect_db()
        daily_query = f"""
            SELECT DATE(timestamp) as date,
                   COUNT(*) as message_count,
                   AVG(response_time) as avg_response_time,
                   AVG(confidence_score) as avg_confidence
            FROM messages 
            WHERE timestamp >= datetime('now', '-{days} days')
            GROUP BY DATE(timestamp)
            ORDER BY date
        """
        
        daily_df = pd.read_sql_query(daily_query, conn)
        
        # Get popular questions
        popular_query = f"""
            SELECT user_message, COUNT(*) as frequency
            FROM messages 
            WHERE timestamp >= datetime('now', '-{days} days')
            GROUP BY user_message
            ORDER BY frequency DESC
            LIMIT 10
        """
        
        popular_df = pd.read_sql_query(popular_query, conn)
        conn.close()
        
        # Compile report
        report = {
            'report_date': datetime.now().isoformat(),
            'period_days': days,
            'overall_metrics': overall_metrics,
            'daily_breakdown': daily_df.to_dict('records') if not daily_df.empty else [],
            'popular_questions': popular_df.to_dict('records') if not popular_df.empty else [],
            'performance_grade': self.calculate_performance_grade(overall_metrics)
        }
        
        return report
    
    def calculate_performance_grade(self, metrics: Dict) -> str:
        """Calculate overall performance grade"""
        if 'error' in metrics:
            return 'N/A'
        
        # Scoring criteria
        score = 0
        max_score = 100
        
        # Response time (30 points)
        avg_response_time = metrics.get('avg_response_time', float('inf'))
        if avg_response_time < 1.0:
            score += 30
        elif avg_response_time < 2.0:
            score += 25
        elif avg_response_time < 3.0:
            score += 20
        elif avg_response_time < 5.0:
            score += 15
        else:
            score += 10
        
        # Confidence (25 points)
        avg_confidence = metrics.get('avg_confidence', 0)
        score += min(avg_confidence * 25, 25)
        
        # Semantic similarity (20 points)
        semantic_sim = metrics.get('avg_semantic_similarity', 0)
        score += min(semantic_sim * 20, 20)
        
        # Completeness (15 points)
        completeness = metrics.get('avg_completeness_score', 0)
        score += min(completeness * 15, 15)
        
        # Politeness (10 points)
        politeness = metrics.get('avg_politeness_score', 0)
        score += min(politeness * 10, 10)
        
        # Convert to grade
        percentage = (score / max_score) * 100
        
        if percentage >= 90:
            return 'A+ (Xuất sắc)'
        elif percentage >= 80:
            return 'A (Tốt)'
        elif percentage >= 70:
            return 'B (Khá)'
        elif percentage >= 60:
            return 'C (Trung bình)'
        else:
            return 'D (Cần cải thiện)'
    
    def create_visualizations(self, report: Dict, save_path: str = "vqa_metrics_report.png"):
        """Create visualization charts"""
        try:
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            fig.suptitle('E-commerce VQA Chatbot Performance Dashboard', fontsize=16, fontweight='bold')
            
            # Daily message count
            if report['daily_breakdown']:
                daily_df = pd.DataFrame(report['daily_breakdown'])
                axes[0, 0].plot(daily_df['date'], daily_df['message_count'], marker='o')
                axes[0, 0].set_title('Daily Message Count')
                axes[0, 0].set_xlabel('Date')
                axes[0, 0].set_ylabel('Messages')
                axes[0, 0].tick_params(axis='x', rotation=45)
            
            # Response time distribution
            if report['daily_breakdown']:
                axes[0, 1].plot(daily_df['date'], daily_df['avg_response_time'], marker='s', color='orange')
                axes[0, 1].set_title('Average Response Time')
                axes[0, 1].set_xlabel('Date')
                axes[0, 1].set_ylabel('Response Time (s)')
                axes[0, 1].tick_params(axis='x', rotation=45)
            
            # Confidence scores
            if report['daily_breakdown']:
                axes[1, 0].plot(daily_df['date'], daily_df['avg_confidence'], marker='^', color='green')
                axes[1, 0].set_title('Average Confidence Score')
                axes[1, 0].set_xlabel('Date')
                axes[1, 0].set_ylabel('Confidence')
                axes[1, 0].tick_params(axis='x', rotation=45)
            
            # Popular questions
            if report['popular_questions']:
                popular_df = pd.DataFrame(report['popular_questions'])
                top_5 = popular_df.head(5)
                axes[1, 1].barh(range(len(top_5)), top_5['frequency'])
                axes[1, 1].set_yticks(range(len(top_5)))
                axes[1, 1].set_yticklabels([q[:30] + '...' if len(q) > 30 else q 
                                          for q in top_5['user_message']])
                axes[1, 1].set_title('Top 5 Popular Questions')
                axes[1, 1].set_xlabel('Frequency')
            
            plt.tight_layout()
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"📈 Visualization saved to {save_path}")
            
        except Exception as e:
            print(f"Error creating visualizations: {e}")
    
    def export_detailed_report(self, days: int = 7, filename: str = "vqa_detailed_report.json"):
        """Export detailed performance report"""
        report = self.generate_performance_report(days)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2, default=str)
        
        print(f"📄 Detailed report exported to {filename}")
        return report

def main():
    """Main function to run metrics analysis"""
    print("🔍 E-commerce VQA Metrics Analysis")
    print("=" * 50)
    
    # Initialize metrics analyzer
    analyzer = EcommerceVQAMetrics()
    
    # Generate report
    try:
        report = analyzer.generate_performance_report(days=7)
        
        # Print summary
        print("\n📊 PERFORMANCE SUMMARY")
        print("-" * 30)
        
        if 'error' not in report['overall_metrics']:
            metrics = report['overall_metrics']
            print(f"📈 Total Conversations: {metrics.get('total_conversations', 0)}")
            print(f"👥 Unique Sessions: {metrics.get('unique_sessions', 0)}")
            print(f"⏱️ Avg Response Time: {metrics.get('avg_response_time', 0):.2f}s")
            print(f"🎯 Avg Confidence: {metrics.get('avg_confidence', 0):.2f}")
            print(f"🔤 Avg BLEU Score: {metrics.get('avg_bleu_score', 0):.3f}")
            print(f"📝 Avg ROUGE-L: {metrics.get('avg_rougeL', 0):.3f}")
            print(f"🧠 Avg Semantic Similarity: {metrics.get('avg_semantic_similarity', 0):.3f}")
            print(f"🏆 Performance Grade: {report['performance_grade']}")
        else:
            print("❌ No data available for analysis")
        
        # Create visualizations
        analyzer.create_visualizations(report)
        
        # Export detailed report
        analyzer.export_detailed_report()
        
        print("\n✅ Metrics analysis completed!")
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")

if __name__ == "__main__":
    main()
