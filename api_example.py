
# V<PERSON> dụ sử dụng model qua API

from transformers import AutoTokenizer, AutoModelForCausalLM
import torch
from PIL import Image

class VinternVQA:
    def __init__(self, model_path="./finetuned-vintern-ocr-vqa"):
        self.tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
        self.model = AutoModelForCausalLM.from_pretrained(
            model_path, 
            trust_remote_code=True,
            torch_dtype=torch.float16,
            device_map="auto"
        )
        
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
    
    def ask(self, question, image_path=None):
        """Hỏi câu hỏi về ảnh"""
        prompt = f"<|im_start|>user\n{question}<|im_end|>\n<|im_start|>assistant\n"
        
        inputs = self.tokenizer(prompt, return_tensors="pt", max_length=512, truncation=True)
        
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=100,
                do_sample=True,
                temperature=0.7,
                pad_token_id=self.tokenizer.pad_token_id
            )
        
        response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # Extract answer
        if "<|im_start|>assistant\n" in response:
            answer = response.split("<|im_start|>assistant\n")[-1]
            if "<|im_end|>" in answer:
                answer = answer.split("<|im_end|>")[0]
        else:
            answer = response
            
        return answer.strip()

# Sử dụng
vqa = VinternVQA()

# Hỏi về ảnh
answer = vqa.ask("Có gì trong ảnh này?", "path/to/image.jpg")
print(f"Câu trả lời: {answer}")
