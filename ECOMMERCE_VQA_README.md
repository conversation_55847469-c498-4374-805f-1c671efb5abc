# 🛒 E-commerce VQA Chatbot System

## 📋 Tổng quan

Hệ thống chatbot hỏi đáp về sản phẩm e-commerce sử dụng mô hình Vintern-1B-v3_5 đã được fine-tune trên bộ dữ liệu Viet-OCR-VQA. Hệ thống cho phép khách hàng gửi hình ảnh sản phẩm và đặt câu hỏi bằng tiếng Việt, nhận được câu trả lời tự động và chính xác.

## 🎯 Tính năng chính

### 💬 Chatbot Interface
- **Upload ảnh sản phẩm** và đặt câu hỏi
- **Giao diện responsive** hoạt động trên mọi thiết bị
- **Real-time chat** với typing indicators
- **Suggested questions** để cải thiện UX
- **Session management** lưu trữ lịch sử chat

### 🤖 AI Capabilities
- **Visual Question Answering** bằng tiếng Việt
- **Product recognition** và phân tích hình ảnh
- **Context-aware responses** dựa trên lịch sử chat
- **Confidence scoring** cho mỗi câu trả lời
- **Fallback handling** khi gặp lỗi

### 📊 Analytics & Metrics
- **Real-time performance metrics**
- **BLEU, ROUGE scores** cho chất lượng response
- **Semantic similarity analysis**
- **Response time monitoring**
- **User engagement tracking**

### 🛒 E-commerce Integration
- **Product search** và lookup
- **Inventory checking**
- **Price information**
- **Product recommendations**

## 🏗️ Kiến trúc hệ thống

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   AI Model      │
│   (HTML/JS)     │◄──►│   (FastAPI)     │◄──►│   (Vintern)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐
                       │   Database      │
                       │   (SQLite)      │
                       └─────────────────┘
```

## 🚀 Cài đặt và Triển khai

### 1. Cài đặt tự động
```bash
python deploy_ecommerce_vqa.py
```

### 2. Cài đặt thủ công

#### Yêu cầu hệ thống
- Python 3.8+
- 8GB+ RAM
- GPU (khuyến nghị) hoặc CPU
- 10GB dung lượng trống

#### Cài đặt dependencies
```bash
pip install -r requirements_ecommerce.txt
```

#### Khởi động server
```bash
python ecommerce_vqa_backend.py
```

### 3. Sử dụng Docker
```bash
docker-compose up
```

## 📖 Sử dụng

### Web Interface
1. Truy cập: `http://localhost:8000/chat`
2. Upload ảnh sản phẩm (tùy chọn)
3. Nhập câu hỏi bằng tiếng Việt
4. Nhận câu trả lời từ AI

### API Endpoints

#### Chat API
```bash
POST /api/chat
Content-Type: multipart/form-data

{
  "question": "Sản phẩm này có màu nào khác không?",
  "session_id": "optional-session-id",
  "image": "image-file"
}
```

#### Metrics API
```bash
GET /api/metrics
```

#### Chat History
```bash
GET /api/sessions/{session_id}/history
```

## 📊 Đánh giá và Metrics

### Chạy đánh giá
```bash
python ecommerce_vqa_metrics.py
```

### Metrics được theo dõi

#### 🎯 Quality Metrics
- **BLEU Score**: Đo độ chính xác ngôn ngữ
- **ROUGE Score**: Đo độ tương đồng nội dung
- **Semantic Similarity**: Đo độ tương đồng ngữ nghĩa
- **Confidence Score**: Độ tin cậy của model

#### ⚡ Performance Metrics
- **Response Time**: Thời gian phản hồi
- **Throughput**: Số requests/giây
- **Success Rate**: Tỷ lệ thành công
- **Error Rate**: Tỷ lệ lỗi

#### 🛒 E-commerce Metrics
- **Product Recognition Accuracy**: Độ chính xác nhận diện sản phẩm
- **Query Category Distribution**: Phân bố loại câu hỏi
- **User Engagement**: Mức độ tương tác người dùng

### Kết quả đánh giá mẫu

```
📊 PERFORMANCE SUMMARY
─────────────────────────────
📈 Total Conversations: 150
👥 Unique Sessions: 45
⏱️ Avg Response Time: 1.85s
🎯 Avg Confidence: 0.72
🔤 Avg BLEU Score: 0.245
📝 Avg ROUGE-L: 0.312
🧠 Avg Semantic Similarity: 0.681
🏆 Performance Grade: A (Tốt)
```

## 🧪 Testing

### Chạy test tự động
```bash
python test_ecommerce_vqa_system.py
```

### Test cases bao gồm
- ✅ API health checks
- ✅ Chat functionality với/không ảnh
- ✅ Metrics endpoints
- ✅ Session management
- ✅ Performance testing
- ✅ E-commerce features

## 📁 Cấu trúc dự án

```
ecommerce-vqa-chatbot/
├── 🎯 Core Components
│   ├── ecommerce_vqa_backend.py      # FastAPI backend
│   ├── ecommerce_chatbot_frontend.html # Web interface
│   └── ecommerce_vqa_metrics.py      # Metrics system
│
├── 🧪 Testing & Deployment
│   ├── test_ecommerce_vqa_system.py  # Test suite
│   ├── deploy_ecommerce_vqa.py       # Deployment script
│   └── requirements_ecommerce.txt    # Dependencies
│
├── 🐳 Docker
│   ├── Dockerfile
│   ├── docker-compose.yml
│   └── nginx.conf
│
└── 📊 Data & Models
    ├── ecommerce_vqa.db              # SQLite database
    ├── finetuned-vintern-ocr-vqa/    # Fine-tuned model
    └── demo-finetuned-model/         # Fallback model
```

## 🔧 Cấu hình

### Environment Variables
```bash
export MODEL_PATH="./finetuned-vintern-ocr-vqa"
export DATABASE_URL="sqlite:///ecommerce_vqa.db"
export API_HOST="0.0.0.0"
export API_PORT="8000"
```

### Model Configuration
```python
# Trong ecommerce_vqa_backend.py
model_config = {
    "max_new_tokens": 150,
    "temperature": 0.7,
    "top_p": 0.9,
    "do_sample": True
}
```

## 🎨 Customization

### Thêm câu hỏi gợi ý
```python
def get_suggested_questions(self, question: str, answer: str) -> List[str]:
    suggestions = [
        "Sản phẩm này có màu nào khác không?",
        "Giá của sản phẩm này là bao nhiêu?",
        # Thêm câu hỏi tùy chỉnh
    ]
    return suggestions
```

### Tùy chỉnh giao diện
- Chỉnh sửa `ecommerce_chatbot_frontend.html`
- Thay đổi CSS styles
- Thêm tính năng JavaScript

### Tích hợp database sản phẩm
```python
@app.get("/api/products/search")
async def search_products(query: str):
    # Tích hợp với database thực tế
    products = your_product_database.search(query)
    return {"results": products}
```

## 🔒 Bảo mật

### API Security
- Rate limiting
- Input validation
- File upload restrictions
- SQL injection prevention

### Data Privacy
- Session data encryption
- Image data handling
- User privacy protection

## 📈 Monitoring

### Health Checks
```bash
curl http://localhost:8000/health
```

### Metrics Dashboard
- Truy cập: `http://localhost:8000/metrics`
- Real-time performance monitoring
- Historical data analysis

## 🐛 Troubleshooting

### Common Issues

#### Model không load được
```bash
# Kiểm tra model path
ls -la ./finetuned-vintern-ocr-vqa/

# Sử dụng fallback model
export USE_DEMO_MODEL=true
```

#### Database errors
```bash
# Reset database
rm ecommerce_vqa.db
python ecommerce_vqa_backend.py
```

#### Performance issues
```bash
# Giảm batch size
export MODEL_BATCH_SIZE=1

# Sử dụng CPU mode
export FORCE_CPU=true
```

## 🤝 Đóng góp

### Development Setup
```bash
git clone <repository>
cd ecommerce-vqa-chatbot
pip install -r requirements_dev.txt
pre-commit install
```

### Testing
```bash
pytest tests/
python test_ecommerce_vqa_system.py
```

## 📄 License

MIT License - xem file LICENSE để biết chi tiết

## 📞 Hỗ trợ

- 📧 Email: <EMAIL>
- 📖 Documentation: [Link to docs]
- 🐛 Issues: [GitHub Issues]
- 💬 Community: [Discord/Slack]

---

**🎉 Cảm ơn bạn đã sử dụng E-commerce VQA Chatbot System!**
