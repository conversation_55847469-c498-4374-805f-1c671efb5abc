
# 📁 CẤU TRÚC DỰ ÁN CHI TIẾT

```
Finr_Tunning_Vintern/
│
├── 🎯 CORE SCRIPTS
│   ├── Fine_tunning_vintern.py      # Script chính fine-tuning Vintern
│   ├── Fine_tuning_demo.py          # Demo với model DialoGPT nhỏ
│   └── run_finetuning.py           # Script tổng hợp thông minh
│
├── 🧪 EVALUATION & TESTING
│   ├── evaluate_model.py            # Đánh giá chất lượng model
│   ├── test_finetuned_model.py      # Test model sau fine-tuning
│   └── check_disk_space.py          # Kiểm tra và quản lý dung lượng
│
├── 📱 APPLICATION & DEPLOYMENT
│   ├── application_guide.py         # Hướng dẫn ứng dụng và Gradio UI
│   ├── api_example.py              # Ví dụ sử dụng API (auto-generated)
│   └── create_documentation.py     # Script tạo tài liệu
│
├── 📋 CONFIGURATION
│   ├── requirements.txt            # Dependencies Python
│   └── .gitignore                  # Git ignore rules
│
├── 📖 DOCUMENTATION
│   ├── README.md                   # Hướng dẫn cơ bản
│   ├── PROJECT_OVERVIEW.md         # Tổng quan dự án (file này)
│   ├── USAGE_GUIDE.md             # Hướng dẫn sử dụng chi tiết
│   ├── SUMMARY_REPORT.md          # Báo cáo tóm tắt
│   └── PROJECT_STRUCTURE.md       # Cấu trúc dự án
│
├── 📊 OUTPUTS (Generated)
│   ├── PROJECT_OVERVIEW.docx      # Document Word
│   ├── evaluation_results.json    # Kết quả đánh giá
│   └── demo-finetuned-model/      # Model demo đã fine-tune
│
└── 🗂️ CACHE & TEMP (Auto-created)
    ├── cache/                      # HuggingFace cache
    ├── demo_cache/                # Cache cho demo
    └── temp_cache/                # Cache tạm thời
```

## 📝 MÔ TẢ CHI TIẾT

### 🎯 Core Scripts
- **Fine_tunning_vintern.py:** Script chính để fine-tune Vintern với dataset Viet-OCR-VQA
- **Fine_tuning_demo.py:** Demo với model DialoGPT nhỏ để test quy trình
- **run_finetuning.py:** Script tổng hợp cho phép chọn demo hoặc full training

### 🧪 Evaluation & Testing
- **evaluate_model.py:** Framework đánh giá chất lượng với metrics tự động
- **test_finetuned_model.py:** Interactive testing với model đã fine-tune
- **check_disk_space.py:** Quản lý dung lượng và dọn dẹp cache

### 📱 Application & Deployment
- **application_guide.py:** Hướng dẫn triển khai và Gradio interface
- **api_example.py:** Template sử dụng model qua API
- **create_documentation.py:** Tự động tạo tài liệu Word/PDF

### 📖 Documentation
- **README.md:** Hướng dẫn quick start
- **PROJECT_OVERVIEW.md:** Tổng quan toàn diện dự án
- **USAGE_GUIDE.md:** Hướng dẫn sử dụng từng component
- **SUMMARY_REPORT.md:** Báo cáo kết quả ngắn gọn

## 🔄 WORKFLOW

1. **Setup:** `pip install -r requirements.txt`
2. **Check:** `python check_disk_space.py`
3. **Demo:** `python run_finetuning.py` → Chọn option 1
4. **Full:** `python run_finetuning.py` → Chọn option 2
5. **Test:** `python test_finetuned_model.py`
6. **Eval:** `python evaluate_model.py`
7. **Deploy:** `python application_guide.py`

## 📊 DEPENDENCIES

### Core ML Libraries
- torch>=2.0.0
- transformers>=4.35.0
- datasets>=2.14.0
- peft>=0.6.0

### Utilities
- pillow>=9.0.0
- numpy>=1.21.0
- matplotlib>=3.5.0
- gradio>=4.0.0

### Documentation
- python-docx
- markdown
- beautifulsoup4
