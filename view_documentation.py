"""
Script để xem tất cả tài liệu dự án
"""

import os
import subprocess
import sys

def list_documentation_files():
    """Liệt kê tất cả file tài liệu"""
    doc_files = {
        "📋 Tổng quan dự án": "PROJECT_OVERVIEW.md",
        "📄 Document Word": "PROJECT_OVERVIEW.docx", 
        "📊 Báo cáo tóm tắt": "SUMMARY_REPORT.md",
        "📁 Cấu trúc dự án": "PROJECT_STRUCTURE.md",
        "📖 Hướng dẫn sử dụng": "USAGE_GUIDE.md",
        "🏆 Đánh giá cuối cùng": "FINAL_ASSESSMENT.md",
        "📚 Hướng dẫn cơ bản": "README.md"
    }
    
    print("📚 TÀI LIỆU DỰ ÁN FINE-TUNING VINTERN")
    print("=" * 50)
    
    available_files = []
    for i, (name, filename) in enumerate(doc_files.items(), 1):
        if os.path.exists(filename):
            print(f"{i}. {name} ({filename}) ✅")
            available_files.append((name, filename))
        else:
            print(f"{i}. {name} ({filename}) ❌")
    
    return available_files

def open_file(filename):
    """Mở file với ứng dụng mặc định"""
    try:
        if sys.platform.startswith('win'):
            os.startfile(filename)
        elif sys.platform.startswith('darwin'):
            subprocess.run(['open', filename])
        else:
            subprocess.run(['xdg-open', filename])
        print(f"✅ Đã mở {filename}")
        return True
    except Exception as e:
        print(f"❌ Lỗi khi mở {filename}: {e}")
        return False

def show_file_content(filename, lines=20):
    """Hiển thị nội dung file"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.readlines()
        
        print(f"\n📄 Nội dung {filename} (20 dòng đầu):")
        print("-" * 50)
        
        for i, line in enumerate(content[:lines], 1):
            print(f"{i:2d}: {line.rstrip()}")
        
        if len(content) > lines:
            print(f"... và {len(content) - lines} dòng nữa")
        
        print("-" * 50)
        return True
        
    except Exception as e:
        print(f"❌ Lỗi khi đọc {filename}: {e}")
        return False

def create_summary_table():
    """Tạo bảng tóm tắt các file"""
    files_info = [
        ("README.md", "Hướng dẫn cơ bản", "Quick start guide"),
        ("PROJECT_OVERVIEW.md", "Tổng quan dự án", "Comprehensive project overview"),
        ("PROJECT_OVERVIEW.docx", "Document Word", "Professional document format"),
        ("SUMMARY_REPORT.md", "Báo cáo tóm tắt", "Executive summary"),
        ("PROJECT_STRUCTURE.md", "Cấu trúc dự án", "Project architecture"),
        ("USAGE_GUIDE.md", "Hướng dẫn sử dụng", "Detailed usage instructions"),
        ("FINAL_ASSESSMENT.md", "Đánh giá cuối cùng", "Final evaluation and metrics")
    ]
    
    print("\n📊 BẢNG TÓM TẮT TÀI LIỆU")
    print("=" * 80)
    print(f"{'File':<25} {'Mô tả':<20} {'Nội dung':<30} {'Status':<5}")
    print("-" * 80)
    
    for filename, description, content in files_info:
        status = "✅" if os.path.exists(filename) else "❌"
        print(f"{filename:<25} {description:<20} {content:<30} {status:<5}")
    
    print("-" * 80)

def main():
    """Hàm chính"""
    print("📖 VIEWER TÀI LIỆU DỰ ÁN")
    print("=" * 50)
    
    # Hiển thị bảng tóm tắt
    create_summary_table()
    
    # Liệt kê file có sẵn
    available_files = list_documentation_files()
    
    if not available_files:
        print("\n❌ Không tìm thấy file tài liệu nào!")
        print("💡 Hãy chạy create_documentation.py trước")
        return
    
    print(f"\n📋 Tìm thấy {len(available_files)} file tài liệu")
    
    while True:
        print("\n🔧 TÙYCHỌN:")
        print("1. Mở file cụ thể")
        print("2. Xem nội dung file")
        print("3. Mở tất cả file")
        print("4. Mở Word document")
        print("5. Thoát")
        
        choice = input("\nChọn (1-5): ").strip()
        
        if choice == "1":
            print("\n📁 Chọn file để mở:")
            for i, (name, filename) in enumerate(available_files, 1):
                print(f"{i}. {name}")
            
            try:
                file_choice = int(input("Chọn số: ")) - 1
                if 0 <= file_choice < len(available_files):
                    _, filename = available_files[file_choice]
                    open_file(filename)
                else:
                    print("❌ Lựa chọn không hợp lệ")
            except ValueError:
                print("❌ Vui lòng nhập số")
        
        elif choice == "2":
            print("\n📄 Chọn file để xem nội dung:")
            for i, (name, filename) in enumerate(available_files, 1):
                print(f"{i}. {name}")
            
            try:
                file_choice = int(input("Chọn số: ")) - 1
                if 0 <= file_choice < len(available_files):
                    _, filename = available_files[file_choice]
                    show_file_content(filename)
                else:
                    print("❌ Lựa chọn không hợp lệ")
            except ValueError:
                print("❌ Vui lòng nhập số")
        
        elif choice == "3":
            print("\n🚀 Mở tất cả file...")
            for name, filename in available_files:
                if filename.endswith('.md'):  # Chỉ mở file markdown
                    open_file(filename)
        
        elif choice == "4":
            if os.path.exists("PROJECT_OVERVIEW.docx"):
                print("\n📄 Mở Word document...")
                open_file("PROJECT_OVERVIEW.docx")
            else:
                print("❌ Không tìm thấy file Word")
        
        elif choice == "5":
            print("\n👋 Tạm biệt!")
            break
        
        else:
            print("❌ Lựa chọn không hợp lệ")

if __name__ == "__main__":
    main()
