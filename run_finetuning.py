"""
Script chính để chạy toàn bộ quy trình fine-tuning Vintern
"""

import subprocess
import sys
import os

def check_requirements():
    """Kiểm tra và cài đặt requirements"""
    print("📦 Kiểm tra dependencies...")
    
    try:
        # Kiểm tra xem requirements.txt có tồn tại không
        if not os.path.exists("requirements.txt"):
            print("❌ Không tìm thấy requirements.txt")
            return False
        
        # Cài đặt requirements
        print("🔧 Đang cài đặt dependencies...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Đã cài đặt dependencies thành công")
            return True
        else:
            print(f"❌ Lỗi khi cài đặt dependencies: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Lỗi: {e}")
        return False

def check_disk_space():
    """Kiểm tra dung lượng ổ đĩa"""
    print("\n💾 Kiểm tra dung lượng ổ đĩa...")
    
    try:
        result = subprocess.run([
            sys.executable, "check_disk_space.py"
        ], capture_output=True, text=True)
        
        print(result.stdout)
        
        if "CẢNH BÁO" in result.stdout:
            response = input("⚠️ Ổ đĩa có ít dung lượng. Bạn có muốn tiếp tục? (y/n): ")
            return response.lower() == 'y'
        
        return True
        
    except Exception as e:
        print(f"❌ Lỗi khi kiểm tra ổ đĩa: {e}")
        return True  # Tiếp tục nếu không kiểm tra được

def run_finetuning():
    """Chạy fine-tuning với lựa chọn thông minh"""
    print("\n🚀 Chọn phương thức fine-tuning...")
    print("1. Demo nhanh với model nhỏ (khuyến nghị)")
    print("2. Fine-tuning Vintern đầy đủ (cần GPU mạnh)")

    choice = input("Chọn (1/2): ").strip()

    if choice == "1":
        script_name = "Fine_tuning_demo.py"
        print("🎯 Chạy demo với model nhỏ...")
    elif choice == "2":
        script_name = "Fine_tunning_vintern.py"
        print("🎯 Chạy fine-tuning Vintern đầy đủ...")
    else:
        print("❌ Lựa chọn không hợp lệ")
        return False

    try:
        # Chạy script được chọn
        result = subprocess.run([
            sys.executable, script_name
        ], text=True)

        if result.returncode == 0:
            print("✅ Fine-tuning hoàn thành thành công!")
            return True
        else:
            print("❌ Fine-tuning gặp lỗi")
            return False

    except KeyboardInterrupt:
        print("\n⏹️ Fine-tuning bị dừng bởi người dùng")
        return False
    except Exception as e:
        print(f"❌ Lỗi khi chạy fine-tuning: {e}")
        return False

def test_model():
    """Test model sau khi fine-tuning"""
    print("\n🧪 Bạn có muốn test model ngay? (y/n): ", end="")
    response = input().strip().lower()
    
    if response == 'y':
        try:
            subprocess.run([sys.executable, "test_finetuned_model.py"])
        except Exception as e:
            print(f"❌ Lỗi khi test model: {e}")

def main():
    """Hàm chính"""
    print("🎯 Fine-tuning Vintern với Viet-OCR-VQA Dataset")
    print("=" * 60)
    
    # 1. Kiểm tra requirements
    if not check_requirements():
        print("❌ Không thể tiếp tục do lỗi dependencies")
        return
    
    # 2. Kiểm tra dung lượng ổ đĩa
    if not check_disk_space():
        print("❌ Dừng do không đủ dung lượng ổ đĩa")
        return
    
    # 3. Chạy fine-tuning
    success = run_finetuning()
    
    # 4. Test model nếu thành công
    if success:
        test_model()
        
        print("\n🎉 Hoàn thành!")
        print("📁 Model đã được lưu tại: ./finetuned-vintern-ocr-vqa")
        print("💡 Sử dụng test_finetuned_model.py để test model")
    else:
        print("\n💡 Gợi ý khắc phục lỗi:")
        print("   - Kiểm tra dung lượng ổ đĩa")
        print("   - Giảm batch_size trong Fine_tunning_vintern.py")
        print("   - Kiểm tra kết nối internet")
        print("   - Đảm bảo GPU có đủ VRAM")

if __name__ == "__main__":
    main()
