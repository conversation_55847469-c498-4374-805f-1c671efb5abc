"""
Deployment script cho E-commerce VQA Chatbot System
"""

import subprocess
import sys
import os
import time
import requests
from pathlib import Path

def install_requirements():
    """Cài đặt các dependencies cần thiết"""
    requirements = [
        "fastapi>=0.104.0",
        "uvicorn[standard]>=0.24.0",
        "python-multipart>=0.0.6",
        "pillow>=9.0.0",
        "torch>=2.0.0",
        "transformers>=4.35.0",
        "sentence-transformers>=2.2.0",
        "nltk>=3.8",
        "rouge-score>=0.1.2",
        "scikit-learn>=1.3.0",
        "pandas>=2.0.0",
        "matplotlib>=3.7.0",
        "seaborn>=0.12.0",
        "sqlite3"
    ]
    
    print("📦 Installing requirements...")
    for req in requirements:
        try:
            if req == "sqlite3":
                continue  # Built-in module
            print(f"Installing {req}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", req])
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {req}: {e}")
            return False
    
    print("✅ All requirements installed successfully")
    return True

def check_model_availability():
    """Kiểm tra model có sẵn không"""
    model_paths = [
        "./finetuned-vintern-ocr-vqa",
        "./demo-finetuned-model"
    ]
    
    for path in model_paths:
        if os.path.exists(path):
            print(f"✅ Found model at: {path}")
            return True
    
    print("⚠️ No fine-tuned model found. Will use fallback model.")
    return False

def setup_database():
    """Thiết lập database"""
    print("🗄️ Setting up database...")
    
    try:
        import sqlite3
        conn = sqlite3.connect("ecommerce_vqa.db")
        cursor = conn.cursor()
        
        # Create tables
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sessions (
                session_id TEXT PRIMARY KEY,
                created_at TIMESTAMP,
                last_activity TIMESTAMP,
                message_count INTEGER DEFAULT 0
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS messages (
                message_id TEXT PRIMARY KEY,
                session_id TEXT,
                user_message TEXT,
                bot_response TEXT,
                image_url TEXT,
                timestamp TIMESTAMP,
                response_time REAL,
                confidence_score REAL,
                FOREIGN KEY (session_id) REFERENCES sessions (session_id)
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS analytics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date DATE,
                total_messages INTEGER,
                avg_response_time REAL,
                avg_confidence_score REAL,
                unique_sessions INTEGER
            )
        """)
        
        conn.commit()
        conn.close()
        
        print("✅ Database setup completed")
        return True
        
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        return False

def download_nltk_data():
    """Download NLTK data"""
    print("📚 Downloading NLTK data...")
    
    try:
        import nltk
        nltk.download('punkt', quiet=True)
        nltk.download('stopwords', quiet=True)
        print("✅ NLTK data downloaded")
        return True
    except Exception as e:
        print(f"❌ NLTK download failed: {e}")
        return False

def create_startup_script():
    """Tạo script khởi động"""
    startup_script = """#!/bin/bash
# E-commerce VQA Chatbot Startup Script

echo "🚀 Starting E-commerce VQA Chatbot..."

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install requirements
echo "📦 Installing requirements..."
pip install -r requirements_ecommerce.txt

# Start the server
echo "🌐 Starting server..."
python ecommerce_vqa_backend.py
"""
    
    with open("start_chatbot.sh", "w", encoding="utf-8") as f:
        f.write(startup_script)
    
    # Make executable
    os.chmod("start_chatbot.sh", 0o755)
    print("✅ Startup script created: start_chatbot.sh")

def create_requirements_file():
    """Tạo requirements file"""
    requirements_content = """fastapi>=0.104.0
uvicorn[standard]>=0.24.0
python-multipart>=0.0.6
pillow>=9.0.0
torch>=2.0.0
transformers>=4.35.0
sentence-transformers>=2.2.0
nltk>=3.8
rouge-score>=0.1.2
scikit-learn>=1.3.0
pandas>=2.0.0
matplotlib>=3.7.0
seaborn>=0.12.0
pydantic>=2.0.0
"""
    
    with open("requirements_ecommerce.txt", "w") as f:
        f.write(requirements_content)
    
    print("✅ Requirements file created: requirements_ecommerce.txt")

def test_api_endpoints():
    """Test API endpoints"""
    print("🧪 Testing API endpoints...")
    
    base_url = "http://localhost:8000"
    
    # Test endpoints
    endpoints = [
        "/",
        "/docs",
        "/api/metrics"
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            if response.status_code == 200:
                print(f"✅ {endpoint} - OK")
            else:
                print(f"⚠️ {endpoint} - Status: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ {endpoint} - Error: {e}")

def start_server():
    """Khởi động server"""
    print("🌐 Starting E-commerce VQA Chatbot server...")
    
    try:
        # Start server in background
        process = subprocess.Popen([
            sys.executable, "ecommerce_vqa_backend.py"
        ])
        
        # Wait a bit for server to start
        time.sleep(5)
        
        # Test if server is running
        try:
            response = requests.get("http://localhost:8000", timeout=5)
            if response.status_code == 200:
                print("✅ Server started successfully!")
                print("🌐 Access the chatbot at: http://localhost:8000/chat")
                print("📖 API documentation at: http://localhost:8000/docs")
                return process
            else:
                print("❌ Server not responding correctly")
                return None
        except requests.exceptions.RequestException:
            print("❌ Server failed to start")
            return None
            
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        return None

def create_docker_files():
    """Tạo Docker files cho deployment"""
    
    # Dockerfile
    dockerfile_content = """FROM python:3.10-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    gcc \\
    g++ \\
    && rm -rf /var/lib/apt/lists/*

# Copy requirements
COPY requirements_ecommerce.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements_ecommerce.txt

# Copy application files
COPY . .

# Create database directory
RUN mkdir -p /app/data

# Expose port
EXPOSE 8000

# Start command
CMD ["python", "ecommerce_vqa_backend.py"]
"""
    
    with open("Dockerfile", "w") as f:
        f.write(dockerfile_content)
    
    # Docker Compose
    docker_compose_content = """version: '3.8'

services:
  ecommerce-vqa:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./data:/app/data
      - ./models:/app/models
    environment:
      - PYTHONPATH=/app
    restart: unless-stopped
    
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - ecommerce-vqa
    restart: unless-stopped
"""
    
    with open("docker-compose.yml", "w") as f:
        f.write(docker_compose_content)
    
    # Nginx config
    nginx_config = """events {
    worker_connections 1024;
}

http {
    upstream app {
        server ecommerce-vqa:8000;
    }
    
    server {
        listen 80;
        
        location / {
            proxy_pass http://app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}
"""
    
    with open("nginx.conf", "w") as f:
        f.write(nginx_config)
    
    print("✅ Docker files created")

def main():
    """Main deployment function"""
    print("🚀 E-commerce VQA Chatbot Deployment")
    print("=" * 50)
    
    # Check if required files exist
    required_files = [
        "ecommerce_vqa_backend.py",
        "ecommerce_chatbot_frontend.html",
        "ecommerce_vqa_metrics.py"
    ]
    
    for file in required_files:
        if not os.path.exists(file):
            print(f"❌ Required file missing: {file}")
            return False
    
    print("✅ All required files found")
    
    # Create requirements file
    create_requirements_file()
    
    # Install requirements
    if not install_requirements():
        print("❌ Failed to install requirements")
        return False
    
    # Download NLTK data
    download_nltk_data()
    
    # Check model availability
    check_model_availability()
    
    # Setup database
    if not setup_database():
        print("❌ Failed to setup database")
        return False
    
    # Create deployment files
    create_startup_script()
    create_docker_files()
    
    print("\n🎉 Deployment setup completed!")
    print("\n📋 Next steps:")
    print("1. Start the server: python ecommerce_vqa_backend.py")
    print("2. Or use startup script: ./start_chatbot.sh")
    print("3. Or use Docker: docker-compose up")
    print("4. Access chatbot: http://localhost:8000/chat")
    print("5. View metrics: http://localhost:8000/api/metrics")
    
    # Ask if user wants to start server now
    start_now = input("\n🚀 Start server now? (y/n): ").strip().lower()
    
    if start_now == 'y':
        process = start_server()
        if process:
            try:
                print("\n⌨️ Press Ctrl+C to stop the server")
                process.wait()
            except KeyboardInterrupt:
                print("\n🛑 Stopping server...")
                process.terminate()
                process.wait()
                print("✅ Server stopped")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
