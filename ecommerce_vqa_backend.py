"""
E-commerce VQA Chatbot Backend API
Sử dụng Vintern fine-tuned model để trả lời câu hỏi về sản phẩm
"""

from fastapi import FastAPI, File, UploadFile, Form, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from PIL import Image
import io
import base64
import sqlite3
import uuid
import time
import json
from datetime import datetime
from typing import Optional, List, Dict
import asyncio
from pydantic import BaseModel
import logging
import os

# C<PERSON>u hình logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Pydantic models
class ChatMessage(BaseModel):
    message_id: str
    session_id: str
    user_message: str
    bot_response: str
    image_url: Optional[str] = None
    timestamp: datetime
    response_time: float
    confidence_score: float

class SessionInfo(BaseModel):
    session_id: str
    created_at: datetime
    last_activity: datetime
    message_count: int

class VQARequest(BaseModel):
    question: str
    session_id: Optional[str] = None

class VQAResponse(BaseModel):
    answer: str
    session_id: str
    message_id: str
    confidence_score: float
    response_time: float
    suggested_questions: List[str]

# FastAPI app
app = FastAPI(
    title="E-commerce VQA Chatbot API",
    description="Hệ thống chatbot hỏi đáp về sản phẩm sử dụng Vintern model",
    version="1.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables
model = None
tokenizer = None
db_connection = None

class VinternVQAModel:
    def __init__(self, model_path="microsoft/DialoGPT-small"):
        self.model_path = model_path
        self.model = None
        self.tokenizer = None
        self.device = "cpu"  # Force CPU for stability

    def load_model(self):
        """Load model with fallback strategy"""
        try:
            # Try Vintern first if available
            vintern_path = "./finetuned-vintern-ocr-vqa"
            if os.path.exists(vintern_path) and os.path.exists(os.path.join(vintern_path, "config.json")):
                logger.info(f"Attempting to load Vintern model from {vintern_path}")
                if self.load_vintern_model(vintern_path):
                    return True

            # Skip demo model, go directly to DialoGPT
            logger.info("Loading DialoGPT model as fallback...")
            return self.load_dialogpt_model()

        except Exception as e:
            logger.error(f"Error in load_model: {e}")
            return self.load_dialogpt_model()

    def load_vintern_model(self, model_path):
        """Load Vintern model"""
        try:
            self.tokenizer = AutoTokenizer.from_pretrained(
                model_path,
                trust_remote_code=True
            )

            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token

            self.model = AutoModelForCausalLM.from_pretrained(
                model_path,
                trust_remote_code=True,
                torch_dtype=torch.float32,
                device_map="cpu"
            )

            logger.info("Vintern model loaded successfully")
            return True

        except Exception as e:
            logger.error(f"Error loading Vintern model: {e}")
            return False
    
    def load_demo_model(self, model_path):
        """Load demo model from path"""
        try:
            self.tokenizer = AutoTokenizer.from_pretrained(model_path)
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token

            self.model = AutoModelForCausalLM.from_pretrained(
                model_path,
                torch_dtype=torch.float32,
                device_map="cpu"
            )
            logger.info("Demo model loaded successfully")
            return True

        except Exception as e:
            logger.error(f"Error loading demo model: {e}")
            return False

    def load_dialogpt_model(self):
        """Load DialoGPT as ultimate fallback"""
        try:
            logger.info("Loading DialoGPT-small as fallback...")
            self.tokenizer = AutoTokenizer.from_pretrained("microsoft/DialoGPT-small")
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token

            self.model = AutoModelForCausalLM.from_pretrained(
                "microsoft/DialoGPT-small",
                torch_dtype=torch.float32
            )

            # Move to CPU
            self.model = self.model.to("cpu")

            logger.info("DialoGPT model loaded successfully")
            return True

        except Exception as e:
            logger.error(f"Error loading DialoGPT model: {e}")
            return False
    
    def generate_response(self, question: str, image_data: Optional[bytes] = None) -> Dict:
        """Generate response for VQA"""
        start_time = time.time()

        try:
            if not self.model or not self.tokenizer:
                logger.error("Model or tokenizer not loaded")
                return {
                    "answer": "Xin lỗi, hệ thống chưa sẵn sàng. Vui lòng thử lại sau.",
                    "response_time": time.time() - start_time,
                    "confidence_score": 0.0,
                    "suggested_questions": []
                }

            # Tạo prompt đơn giản cho DialoGPT
            if "DialoGPT" in str(type(self.model)):
                # DialoGPT format
                prompt = question
                inputs = self.tokenizer.encode(prompt + self.tokenizer.eos_token, return_tensors="pt")
            else:
                # Vintern/other format
                if image_data:
                    prompt = f"Người dùng: Tôi có một hình ảnh sản phẩm. {question}\nTrợ lý:"
                else:
                    prompt = f"Người dùng: {question}\nTrợ lý:"

                inputs = self.tokenizer(
                    prompt,
                    return_tensors="pt",
                    max_length=256,
                    truncation=True
                )

            # Generate với error handling
            with torch.no_grad():
                try:
                    if "DialoGPT" in str(type(self.model)):
                        outputs = self.model.generate(
                            inputs,
                            max_length=inputs.shape[1] + 50,
                            num_return_sequences=1,
                            temperature=0.7,
                            do_sample=True,
                            pad_token_id=self.tokenizer.eos_token_id
                        )
                        # Decode DialoGPT response
                        response = self.tokenizer.decode(outputs[0][inputs.shape[1]:], skip_special_tokens=True)
                    else:
                        outputs = self.model.generate(
                            **inputs,
                            max_new_tokens=100,
                            do_sample=True,
                            temperature=0.7,
                            top_p=0.9,
                            pad_token_id=self.tokenizer.pad_token_id
                        )
                        # Decode other model response
                        generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
                        response = generated_text.replace(prompt, "").strip()

                except Exception as gen_error:
                    logger.error(f"Generation error: {gen_error}")
                    response = self.get_fallback_response(question)

            # Clean up response
            if not response or len(response.strip()) < 3:
                response = self.get_fallback_response(question)

            # Calculate metrics
            response_time = time.time() - start_time
            confidence_score = min(len(response.split()) / 8, 1.0)

            # Generate suggested questions
            suggested_questions = self.get_suggested_questions(question, response)

            return {
                "answer": response.strip(),
                "response_time": response_time,
                "confidence_score": confidence_score,
                "suggested_questions": suggested_questions
            }

        except Exception as e:
            logger.error(f"Error generating response: {e}")
            return {
                "answer": self.get_fallback_response(question),
                "response_time": time.time() - start_time,
                "confidence_score": 0.3,
                "suggested_questions": self.get_default_suggestions()
            }

    def get_fallback_response(self, question: str) -> str:
        """Generate fallback response based on question type"""
        question_lower = question.lower()

        if any(word in question_lower for word in ["màu", "color"]):
            return "Sản phẩm này có nhiều màu sắc khác nhau. Bạn có thể xem thêm các tùy chọn màu trong mô tả sản phẩm."
        elif any(word in question_lower for word in ["giá", "price", "tiền"]):
            return "Để biết giá chính xác, vui lòng liên hệ với bộ phận bán hàng hoặc xem thông tin chi tiết sản phẩm."
        elif any(word in question_lower for word in ["kích thước", "size", "cỡ"]):
            return "Sản phẩm có nhiều kích thước khác nhau. Vui lòng xem bảng size trong mô tả sản phẩm."
        elif any(word in question_lower for word in ["chất liệu", "material"]):
            return "Thông tin về chất liệu được mô tả chi tiết trong phần thông số kỹ thuật của sản phẩm."
        elif any(word in question_lower for word in ["còn hàng", "available", "stock"]):
            return "Vui lòng kiểm tra tình trạng còn hàng trong trang sản phẩm hoặc liên hệ với chúng tôi."
        else:
            return "Cảm ơn bạn đã quan tâm đến sản phẩm. Tôi sẽ cố gắng hỗ trợ bạn tốt nhất có thể. Bạn có thể đặt câu hỏi cụ thể hơn không?"

    def get_default_suggestions(self) -> List[str]:
        """Get default suggested questions"""
        return [
            "Sản phẩm này có màu nào khác không?",
            "Giá của sản phẩm này là bao nhiêu?",
            "Sản phẩm có còn hàng không?"
        ]
    
    def get_suggested_questions(self, question: str, answer: str) -> List[str]:
        """Generate suggested follow-up questions"""
        suggestions = [
            "Sản phẩm này có màu nào khác không?",
            "Giá của sản phẩm này là bao nhiêu?",
            "Sản phẩm có còn hàng không?",
            "Có thông tin về kích thước không?",
            "Chất liệu của sản phẩm là gì?"
        ]
        
        # Filter based on context
        if "màu" in question.lower():
            suggestions = [s for s in suggestions if "màu" not in s.lower()]
        if "giá" in question.lower():
            suggestions = [s for s in suggestions if "giá" not in s.lower()]
            
        return suggestions[:3]

# Database functions
def init_database():
    """Initialize SQLite database"""
    global db_connection
    
    try:
        db_connection = sqlite3.connect("ecommerce_vqa.db", check_same_thread=False)
        cursor = db_connection.cursor()
        
        # Create tables
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sessions (
                session_id TEXT PRIMARY KEY,
                created_at TIMESTAMP,
                last_activity TIMESTAMP,
                message_count INTEGER DEFAULT 0
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS messages (
                message_id TEXT PRIMARY KEY,
                session_id TEXT,
                user_message TEXT,
                bot_response TEXT,
                image_url TEXT,
                timestamp TIMESTAMP,
                response_time REAL,
                confidence_score REAL,
                FOREIGN KEY (session_id) REFERENCES sessions (session_id)
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS analytics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date DATE,
                total_messages INTEGER,
                avg_response_time REAL,
                avg_confidence_score REAL,
                unique_sessions INTEGER
            )
        """)
        
        db_connection.commit()
        logger.info("Database initialized successfully")
        
    except Exception as e:
        logger.error(f"Error initializing database: {e}")

def create_session() -> str:
    """Create new chat session"""
    session_id = str(uuid.uuid4())
    
    try:
        cursor = db_connection.cursor()
        cursor.execute(
            "INSERT INTO sessions (session_id, created_at, last_activity) VALUES (?, ?, ?)",
            (session_id, datetime.now(), datetime.now())
        )
        db_connection.commit()
        
    except Exception as e:
        logger.error(f"Error creating session: {e}")
    
    return session_id

def save_message(session_id: str, user_message: str, bot_response: str, 
                response_time: float, confidence_score: float, image_url: str = None) -> str:
    """Save message to database"""
    message_id = str(uuid.uuid4())
    
    try:
        cursor = db_connection.cursor()
        
        # Save message
        cursor.execute("""
            INSERT INTO messages 
            (message_id, session_id, user_message, bot_response, image_url, timestamp, response_time, confidence_score)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """, (message_id, session_id, user_message, bot_response, image_url, 
              datetime.now(), response_time, confidence_score))
        
        # Update session
        cursor.execute("""
            UPDATE sessions 
            SET last_activity = ?, message_count = message_count + 1
            WHERE session_id = ?
        """, (datetime.now(), session_id))
        
        db_connection.commit()
        
    except Exception as e:
        logger.error(f"Error saving message: {e}")
    
    return message_id

# Startup event
@app.on_event("startup")
async def startup_event():
    """Initialize model and database on startup"""
    global model
    
    logger.info("Starting E-commerce VQA Chatbot API...")
    
    # Initialize database
    init_database()
    
    # Load model
    model = VinternVQAModel()
    if not model.load_model():
        logger.error("Failed to load model")
        raise Exception("Model loading failed")
    
    logger.info("API started successfully")

# API Endpoints
@app.get("/", response_class=HTMLResponse)
async def root():
    """Serve main page"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>E-commerce VQA Chatbot</title>
        <meta charset="utf-8">
    </head>
    <body>
        <h1>🛒 E-commerce VQA Chatbot API</h1>
        <p>API đang chạy thành công!</p>
        <p><a href="/docs">📖 Xem API Documentation</a></p>
        <p><a href="/chat">💬 Mở Chatbot Interface</a></p>
    </body>
    </html>
    """

@app.post("/api/chat", response_model=VQAResponse)
async def chat_endpoint(
    question: str = Form(...),
    session_id: Optional[str] = Form(None),
    image: Optional[UploadFile] = File(None)
):
    """Main chat endpoint"""
    
    # Create session if not provided
    if not session_id:
        session_id = create_session()
    
    # Process image if provided
    image_data = None
    image_url = None
    
    if image:
        try:
            image_data = await image.read()
            # Save image (simplified - in production, use cloud storage)
            image_url = f"/images/{uuid.uuid4()}.jpg"
            
        except Exception as e:
            logger.error(f"Error processing image: {e}")
    
    # Generate response
    result = model.generate_response(question, image_data)
    
    # Save to database
    message_id = save_message(
        session_id=session_id,
        user_message=question,
        bot_response=result["answer"],
        response_time=result["response_time"],
        confidence_score=result["confidence_score"],
        image_url=image_url
    )
    
    return VQAResponse(
        answer=result["answer"],
        session_id=session_id,
        message_id=message_id,
        confidence_score=result["confidence_score"],
        response_time=result["response_time"],
        suggested_questions=result["suggested_questions"]
    )

@app.get("/api/sessions/{session_id}/history")
async def get_chat_history(session_id: str):
    """Get chat history for session"""
    try:
        cursor = db_connection.cursor()
        cursor.execute("""
            SELECT message_id, user_message, bot_response, image_url, timestamp, response_time, confidence_score
            FROM messages
            WHERE session_id = ?
            ORDER BY timestamp ASC
        """, (session_id,))

        messages = []
        for row in cursor.fetchall():
            messages.append({
                "message_id": row[0],
                "user_message": row[1],
                "bot_response": row[2],
                "image_url": row[3],
                "timestamp": row[4],
                "response_time": row[5],
                "confidence_score": row[6]
            })

        return {"session_id": session_id, "messages": messages}

    except Exception as e:
        logger.error(f"Error getting chat history: {e}")
        raise HTTPException(status_code=500, detail="Error retrieving chat history")

@app.get("/api/metrics")
async def get_metrics():
    """Get performance metrics"""
    try:
        from ecommerce_vqa_metrics import EcommerceVQAMetrics
        analyzer = EcommerceVQAMetrics()
        metrics = analyzer.get_conversation_metrics(days=7)
        return metrics
    except Exception as e:
        logger.error(f"Error getting metrics: {e}")
        return {"error": str(e)}

@app.get("/api/analytics/report")
async def get_analytics_report(days: int = 7):
    """Get detailed analytics report"""
    try:
        from ecommerce_vqa_metrics import EcommerceVQAMetrics
        analyzer = EcommerceVQAMetrics()
        report = analyzer.generate_performance_report(days=days)
        return report
    except Exception as e:
        logger.error(f"Error generating report: {e}")
        return {"error": str(e)}

@app.get("/chat", response_class=HTMLResponse)
async def chat_interface():
    """Serve chat interface"""
    try:
        with open("ecommerce_chatbot_frontend.html", "r", encoding="utf-8") as f:
            return f.read()
    except FileNotFoundError:
        return HTMLResponse("""
        <h1>Chat Interface Not Found</h1>
        <p>Please ensure ecommerce_chatbot_frontend.html is in the same directory.</p>
        """, status_code=404)

# E-commerce specific endpoints
@app.get("/api/products/search")
async def search_products(query: str):
    """Search products (mock implementation)"""
    # Mock product database
    mock_products = [
        {"id": 1, "name": "Áo thun cotton", "price": 299000, "color": "Xanh", "available": True},
        {"id": 2, "name": "Quần jeans", "price": 599000, "color": "Đen", "available": True},
        {"id": 3, "name": "Giày sneaker", "price": 899000, "color": "Trắng", "available": False},
        {"id": 4, "name": "Túi xách", "price": 1299000, "color": "Nâu", "available": True},
        {"id": 5, "name": "Đồng hồ", "price": 2999000, "color": "Bạc", "available": True}
    ]

    # Simple search
    results = [p for p in mock_products if query.lower() in p["name"].lower()]
    return {"query": query, "results": results}

@app.get("/api/products/{product_id}")
async def get_product_details(product_id: int):
    """Get product details (mock implementation)"""
    # Mock product details
    mock_product = {
        "id": product_id,
        "name": "Sản phẩm mẫu",
        "price": 499000,
        "description": "Đây là mô tả chi tiết sản phẩm",
        "colors": ["Đỏ", "Xanh", "Vàng"],
        "sizes": ["S", "M", "L", "XL"],
        "material": "Cotton 100%",
        "available": True,
        "stock": 15,
        "rating": 4.5,
        "reviews": 128
    }

    return mock_product

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
