"""
Script kiểm tra server đã sẵn sàng chưa
"""

import requests
import time
import sys

def check_server_ready(max_attempts=30, delay=10):
    """Kiểm tra server có sẵn sàng không"""
    print("🔍 Checking if E-commerce VQA server is ready...")
    
    for attempt in range(max_attempts):
        try:
            response = requests.get("http://localhost:8000/", timeout=5)
            if response.status_code == 200:
                print("✅ Server is ready!")
                print("🌐 Chatbot: http://localhost:8000/chat")
                print("📖 API Docs: http://localhost:8000/docs")
                print("📊 Metrics: http://localhost:8000/api/metrics")
                return True
        except requests.exceptions.RequestException:
            pass
        
        print(f"⏳ Attempt {attempt + 1}/{max_attempts} - Server not ready yet...")
        time.sleep(delay)
    
    print("❌ Server failed to start within expected time")
    return False

if __name__ == "__main__":
    if check_server_ready():
        print("\n🎉 You can now use the E-commerce VQA Chatbot!")
        print("💡 Try asking questions like:")
        print("   - '<PERSON>ản phẩm này có màu nào khác không?'")
        print("   - 'Giá của sản phẩm này là bao nhiêu?'")
        print("   - 'Chất liệu của sản phẩm là gì?'")
    else:
        print("\n💡 If server is still loading, please wait and try again")
        sys.exit(1)
