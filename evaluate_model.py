"""
Script đ<PERSON>h giá chất lượng model sau fine-tuning
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from PIL import Image
import matplotlib.pyplot as plt
import numpy as np
import json
import time
from datetime import datetime
import os

class ModelEvaluator:
    def __init__(self, model_path="./finetuned-vintern-ocr-vqa"):
        self.model_path = model_path
        self.tokenizer = None
        self.model = None
        self.evaluation_results = {}
        
    def load_model(self):
        """Load model đã fine-tune"""
        print("🤖 Đang tải model để đánh giá...")
        try:
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_path, 
                trust_remote_code=True
            )
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_path,
                trust_remote_code=True,
                torch_dtype=torch.float16,
                device_map="auto"
            )
            
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
                
            print("✅ Model đã được tải thành công")
            return True
            
        except Exception as e:
            print(f"❌ Lỗi khi tải model: {e}")
            return False
    
    def create_test_dataset(self):
        """Tạo bộ test chuẩn cho đánh giá"""
        test_cases = [
            {
                "question": "Có gì trong ảnh này?",
                "expected_keywords": ["ảnh", "hình", "màu", "đối tượng"],
                "category": "general_description"
            },
            {
                "question": "Màu sắc chủ đạo là gì?",
                "expected_keywords": ["màu", "xanh", "đỏ", "vàng", "trắng", "đen"],
                "category": "color_recognition"
            },
            {
                "question": "Có bao nhiều đối tượng trong ảnh?",
                "expected_keywords": ["một", "hai", "ba", "nhiều", "ít"],
                "category": "counting"
            },
            {
                "question": "Đây là ảnh về chủ đề gì?",
                "expected_keywords": ["chủ đề", "về", "liên quan", "thuộc"],
                "category": "topic_classification"
            },
            {
                "question": "Mô tả chi tiết ảnh này",
                "expected_keywords": ["mô tả", "chi tiết", "thấy", "có"],
                "category": "detailed_description"
            }
        ]
        return test_cases
    
    def generate_response(self, question, image_path=None):
        """Generate câu trả lời cho câu hỏi"""
        prompt = f"<|im_start|>user\n{question}<|im_end|>\n<|im_start|>assistant\n"
        
        inputs = self.tokenizer(
            prompt,
            return_tensors="pt",
            max_length=512,
            truncation=True
        )
        
        start_time = time.time()
        
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=100,
                do_sample=True,
                temperature=0.7,
                top_p=0.9,
                pad_token_id=self.tokenizer.pad_token_id,
                eos_token_id=self.tokenizer.eos_token_id
            )
        
        generation_time = time.time() - start_time
        
        generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # Lấy phần answer
        if "<|im_start|>assistant\n" in generated_text:
            answer = generated_text.split("<|im_start|>assistant\n")[-1]
            if "<|im_end|>" in answer:
                answer = answer.split("<|im_end|>")[0]
        else:
            answer = generated_text
            
        return answer.strip(), generation_time
    
    def evaluate_response_quality(self, response, expected_keywords):
        """Đánh giá chất lượng response"""
        if not response:
            return 0.0
            
        # Kiểm tra độ dài phù hợp
        length_score = min(len(response.split()) / 10, 1.0)  # Tối đa 10 từ = 1.0
        
        # Kiểm tra từ khóa mong đợi
        keyword_matches = sum(1 for keyword in expected_keywords 
                            if keyword.lower() in response.lower())
        keyword_score = keyword_matches / len(expected_keywords)
        
        # Kiểm tra tính coherent (đơn giản)
        coherence_score = 1.0 if len(response.split()) >= 3 else 0.5
        
        # Tổng điểm
        total_score = (length_score * 0.3 + keyword_score * 0.5 + coherence_score * 0.2)
        
        return min(total_score, 1.0)
    
    def run_evaluation(self):
        """Chạy đánh giá toàn diện"""
        if not self.load_model():
            return None
            
        print("📊 Bắt đầu đánh giá model...")
        
        test_cases = self.create_test_dataset()
        results = {
            "timestamp": datetime.now().isoformat(),
            "model_path": self.model_path,
            "test_results": [],
            "overall_metrics": {}
        }
        
        total_score = 0
        total_time = 0
        category_scores = {}
        
        for i, test_case in enumerate(test_cases):
            print(f"🔍 Test case {i+1}/{len(test_cases)}: {test_case['category']}")
            
            response, gen_time = self.generate_response(test_case["question"])
            quality_score = self.evaluate_response_quality(
                response, test_case["expected_keywords"]
            )
            
            test_result = {
                "question": test_case["question"],
                "response": response,
                "expected_keywords": test_case["expected_keywords"],
                "category": test_case["category"],
                "quality_score": quality_score,
                "generation_time": gen_time
            }
            
            results["test_results"].append(test_result)
            
            total_score += quality_score
            total_time += gen_time
            
            if test_case["category"] not in category_scores:
                category_scores[test_case["category"]] = []
            category_scores[test_case["category"]].append(quality_score)
            
            print(f"   📝 Response: {response[:50]}...")
            print(f"   📊 Score: {quality_score:.2f}")
            print(f"   ⏱️ Time: {gen_time:.2f}s")
        
        # Tính toán metrics tổng quan
        avg_score = total_score / len(test_cases)
        avg_time = total_time / len(test_cases)
        
        category_avg_scores = {
            category: sum(scores) / len(scores) 
            for category, scores in category_scores.items()
        }
        
        results["overall_metrics"] = {
            "average_quality_score": avg_score,
            "average_generation_time": avg_time,
            "total_test_cases": len(test_cases),
            "category_scores": category_avg_scores
        }
        
        self.evaluation_results = results
        return results
    
    def save_results(self, filename="evaluation_results.json"):
        """Lưu kết quả đánh giá"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.evaluation_results, f, ensure_ascii=False, indent=2)
        print(f"💾 Đã lưu kết quả tại: {filename}")
    
    def generate_report(self):
        """Tạo báo cáo đánh giá"""
        if not self.evaluation_results:
            print("❌ Chưa có kết quả đánh giá")
            return
            
        results = self.evaluation_results
        metrics = results["overall_metrics"]
        
        print("\n" + "="*60)
        print("📊 BÁO CÁO ĐÁNH GIÁ MODEL")
        print("="*60)
        print(f"🕒 Thời gian: {results['timestamp']}")
        print(f"📁 Model: {results['model_path']}")
        print(f"🧪 Số test cases: {metrics['total_test_cases']}")
        print()
        
        print("📈 METRICS TỔNG QUAN:")
        print(f"   • Điểm chất lượng trung bình: {metrics['average_quality_score']:.2f}/1.0")
        print(f"   • Thời gian generate trung bình: {metrics['average_generation_time']:.2f}s")
        print()
        
        print("📊 ĐIỂM THEO CATEGORY:")
        for category, score in metrics['category_scores'].items():
            print(f"   • {category}: {score:.2f}/1.0")
        print()
        
        print("📝 CHI TIẾT TEST CASES:")
        for i, test in enumerate(results['test_results']):
            print(f"\n{i+1}. {test['category'].upper()}")
            print(f"   ❓ Question: {test['question']}")
            print(f"   💬 Response: {test['response']}")
            print(f"   📊 Score: {test['quality_score']:.2f}")
            print(f"   ⏱️ Time: {test['generation_time']:.2f}s")
        
        # Đánh giá tổng quan
        overall_score = metrics['average_quality_score']
        if overall_score >= 0.8:
            grade = "🌟 XUẤT SẮC"
        elif overall_score >= 0.6:
            grade = "✅ TỐT"
        elif overall_score >= 0.4:
            grade = "⚠️ TRUNG BÌNH"
        else:
            grade = "❌ CẦN CẢI THIỆN"
            
        print(f"\n🏆 ĐÁNH GIÁ TỔNG QUAN: {grade}")
        print("="*60)

def main():
    """Hàm chính"""
    evaluator = ModelEvaluator()
    
    # Kiểm tra xem có model để đánh giá không
    if not os.path.exists(evaluator.model_path):
        print("❌ Không tìm thấy model để đánh giá")
        print("💡 Hãy chạy fine-tuning trước khi đánh giá")
        return
    
    # Chạy đánh giá
    results = evaluator.run_evaluation()
    
    if results:
        # Lưu kết quả
        evaluator.save_results()
        
        # Tạo báo cáo
        evaluator.generate_report()
        
        print("\n✅ Đánh giá hoàn thành!")
    else:
        print("❌ Đánh giá thất bại!")

if __name__ == "__main__":
    main()
