#!/bin/bash
# E-commerce VQA Chatbot Startup Script

echo "🚀 Starting E-commerce VQA Chatbot..."

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install requirements
echo "📦 Installing requirements..."
pip install -r requirements_ecommerce.txt

# Start the server
echo "🌐 Starting server..."
python ecommerce_vqa_backend.py
