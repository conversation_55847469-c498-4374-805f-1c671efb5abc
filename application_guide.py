"""
Hướng dẫn ứng dụng và sử dụng model đã fine-tune
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from PIL import Image
import gradio as gr
import json
import os

class VinternVQAApp:
    def __init__(self, model_path="./finetuned-vintern-ocr-vqa"):
        self.model_path = model_path
        self.tokenizer = None
        self.model = None
        
    def load_model(self):
        """Load model đã fine-tune"""
        print("🤖 Đang tải model...")
        try:
            self.tokenizer = AutoTokenizer.from_pretrained(
                self.model_path, 
                trust_remote_code=True
            )
            self.model = AutoModelForCausalLM.from_pretrained(
                self.model_path,
                trust_remote_code=True,
                torch_dtype=torch.float16,
                device_map="auto"
            )
            
            if self.tokenizer.pad_token is None:
                self.tokenizer.pad_token = self.tokenizer.eos_token
                
            print("✅ Model đã được tải thành công")
            return True
            
        except Exception as e:
            print(f"❌ Lỗi khi tải model: {e}")
            return False
    
    def answer_question(self, image, question):
        """Trả lời câu hỏi về ảnh"""
        if not self.model or not self.tokenizer:
            return "❌ Model chưa được tải"
        
        if not question.strip():
            return "❌ Vui lòng nhập câu hỏi"
        
        # Tạo prompt
        prompt = f"<|im_start|>user\n{question}<|im_end|>\n<|im_start|>assistant\n"
        
        # Tokenize
        inputs = self.tokenizer(
            prompt,
            return_tensors="pt",
            max_length=512,
            truncation=True
        )
        
        try:
            # Generate response
            with torch.no_grad():
                outputs = self.model.generate(
                    **inputs,
                    max_new_tokens=100,
                    do_sample=True,
                    temperature=0.7,
                    top_p=0.9,
                    pad_token_id=self.tokenizer.pad_token_id,
                    eos_token_id=self.tokenizer.eos_token_id
                )
            
            # Decode response
            generated_text = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
            
            # Extract answer
            if "<|im_start|>assistant\n" in generated_text:
                answer = generated_text.split("<|im_start|>assistant\n")[-1]
                if "<|im_end|>" in answer:
                    answer = answer.split("<|im_end|>")[0]
            else:
                answer = generated_text
                
            return answer.strip()
            
        except Exception as e:
            return f"❌ Lỗi khi generate: {str(e)}"
    
    def create_gradio_interface(self):
        """Tạo giao diện Gradio"""
        def process_image_question(image, question):
            return self.answer_question(image, question)
        
        # Tạo interface
        interface = gr.Interface(
            fn=process_image_question,
            inputs=[
                gr.Image(type="pil", label="📷 Upload ảnh"),
                gr.Textbox(
                    label="❓ Câu hỏi về ảnh", 
                    placeholder="Ví dụ: Có gì trong ảnh này?",
                    lines=2
                )
            ],
            outputs=gr.Textbox(label="💬 Câu trả lời", lines=3),
            title="🤖 Vintern VQA - Hỏi đáp về ảnh bằng tiếng Việt",
            description="""
            ### 🎯 Hướng dẫn sử dụng:
            1. **Upload ảnh** bạn muốn hỏi
            2. **Nhập câu hỏi** bằng tiếng Việt
            3. **Nhấn Submit** để nhận câu trả lời
            
            ### 💡 Ví dụ câu hỏi:
            - "Có gì trong ảnh này?"
            - "Màu sắc chủ đạo là gì?"
            - "Có bao nhiều người trong ảnh?"
            - "Đây là ảnh chụp ở đâu?"
            - "Mô tả chi tiết ảnh này"
            """,
            examples=[
                [None, "Có gì trong ảnh này?"],
                [None, "Màu sắc chủ đạo là gì?"],
                [None, "Có bao nhiều đối tượng trong ảnh?"],
                [None, "Đây là ảnh về chủ đề gì?"],
                [None, "Mô tả chi tiết ảnh này"]
            ],
            theme=gr.themes.Soft(),
            allow_flagging="never"
        )
        
        return interface

def create_api_example():
    """Tạo ví dụ sử dụng API"""
    api_code = '''
# Ví dụ sử dụng model qua API

from transformers import AutoTokenizer, AutoModelForCausalLM
import torch
from PIL import Image

class VinternVQA:
    def __init__(self, model_path="./finetuned-vintern-ocr-vqa"):
        self.tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
        self.model = AutoModelForCausalLM.from_pretrained(
            model_path, 
            trust_remote_code=True,
            torch_dtype=torch.float16,
            device_map="auto"
        )
        
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
    
    def ask(self, question, image_path=None):
        """Hỏi câu hỏi về ảnh"""
        prompt = f"<|im_start|>user\\n{question}<|im_end|>\\n<|im_start|>assistant\\n"
        
        inputs = self.tokenizer(prompt, return_tensors="pt", max_length=512, truncation=True)
        
        with torch.no_grad():
            outputs = self.model.generate(
                **inputs,
                max_new_tokens=100,
                do_sample=True,
                temperature=0.7,
                pad_token_id=self.tokenizer.pad_token_id
            )
        
        response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # Extract answer
        if "<|im_start|>assistant\\n" in response:
            answer = response.split("<|im_start|>assistant\\n")[-1]
            if "<|im_end|>" in answer:
                answer = answer.split("<|im_end|>")[0]
        else:
            answer = response
            
        return answer.strip()

# Sử dụng
vqa = VinternVQA()

# Hỏi về ảnh
answer = vqa.ask("Có gì trong ảnh này?", "path/to/image.jpg")
print(f"Câu trả lời: {answer}")
'''
    
    with open("api_example.py", "w", encoding="utf-8") as f:
        f.write(api_code)
    
    print("💾 Đã tạo file api_example.py")

def create_usage_guide():
    """Tạo hướng dẫn sử dụng chi tiết"""
    guide = """
# 📖 HƯỚNG DẪN SỬ DỤNG MODEL VINTERN VQA

## 🎯 Mục đích và Ứng dụng

### Model này có thể được sử dụng cho:

1. **Hệ thống hỏi đáp về ảnh:**
   - Chatbot hỗ trợ khách hàng với ảnh
   - Ứng dụng giáo dục tương tác
   - Công cụ hỗ trợ người khiếm thị

2. **Phân tích nội dung ảnh:**
   - Mô tả tự động ảnh cho website
   - Kiểm duyệt nội dung hình ảnh
   - Tạo caption cho social media

3. **Ứng dụng thương mại:**
   - E-commerce: mô tả sản phẩm
   - Du lịch: giới thiệu địa điểm
   - Giáo dục: giải thích hình ảnh

## 🚀 Cách sử dụng

### 1. Sử dụng qua Gradio Interface:
```bash
python application_guide.py
```

### 2. Sử dụng qua API:
```python
# Xem file api_example.py
```

### 3. Tích hợp vào ứng dụng:
```python
from vintern_vqa import VinternVQA

vqa = VinternVQA("./finetuned-vintern-ocr-vqa")
answer = vqa.ask("Có gì trong ảnh này?")
```

## ⚠️ Lưu ý quan trọng

### Về hiệu suất:
- Model hoạt động tốt nhất với GPU
- Thời gian response: 1-3 giây/câu hỏi
- RAM yêu cầu: 4-8GB

### Về chất lượng:
- Độ chính xác: 60-80% (tùy loại câu hỏi)
- Tốt nhất với câu hỏi mô tả chung
- Hạn chế với câu hỏi chi tiết phức tạp

### Về ngôn ngữ:
- Chủ yếu hỗ trợ tiếng Việt
- Có thể hiểu một số tiếng Anh đơn giản
- Câu trả lời luôn bằng tiếng Việt

## 🔧 Tùy chỉnh

### Điều chỉnh tham số generation:
```python
outputs = model.generate(
    **inputs,
    max_new_tokens=150,      # Độ dài câu trả lời
    temperature=0.7,         # Tính sáng tạo (0.1-1.0)
    top_p=0.9,              # Đa dạng từ vựng
    do_sample=True          # Bật sampling
)
```

### Tối ưu hiệu suất:
```python
# Sử dụng float16
model = AutoModelForCausalLM.from_pretrained(
    model_path,
    torch_dtype=torch.float16
)

# Sử dụng CPU nếu không có GPU
model = AutoModelForCausalLM.from_pretrained(
    model_path,
    device_map="cpu"
)
```

## 📊 Đánh giá chất lượng

Chạy script đánh giá:
```bash
python evaluate_model.py
```

Kết quả mong đợi:
- Điểm chất lượng: 0.6-0.8/1.0
- Thời gian response: 1-3s
- Độ chính xác theo category khác nhau

## 🐛 Khắc phục sự cố

### Model không load được:
- Kiểm tra đường dẫn model
- Đảm bảo đủ RAM/VRAM
- Thử sử dụng CPU mode

### Câu trả lời không chính xác:
- Điều chỉnh temperature
- Thử câu hỏi rõ ràng hơn
- Kiểm tra chất lượng ảnh input

### Chậm khi inference:
- Sử dụng GPU nếu có
- Giảm max_new_tokens
- Tắt do_sample nếu không cần

## 📈 Cải thiện model

### Fine-tune thêm:
- Thêm dữ liệu domain-specific
- Tăng số epoch training
- Điều chỉnh learning rate

### Tối ưu cho use case cụ thể:
- Tạo prompt template phù hợp
- Filter câu hỏi theo category
- Post-process câu trả lời
"""
    
    with open("USAGE_GUIDE.md", "w", encoding="utf-8") as f:
        f.write(guide)
    
    print("📖 Đã tạo file USAGE_GUIDE.md")

def main():
    """Demo ứng dụng"""
    print("🚀 Vintern VQA Application Demo")
    
    # Tạo các file hướng dẫn
    create_api_example()
    create_usage_guide()
    
    # Khởi tạo app
    app = VinternVQAApp()
    
    # Kiểm tra model
    if not os.path.exists(app.model_path):
        print("❌ Không tìm thấy model đã fine-tune")
        print("💡 Hãy chạy fine-tuning trước khi sử dụng")
        return
    
    # Load model
    if not app.load_model():
        print("❌ Không thể load model")
        return
    
    print("✅ Model đã sẵn sàng!")
    print("\n📋 Các cách sử dụng:")
    print("1. Gradio Interface: Uncomment dòng cuối để chạy")
    print("2. API: Xem file api_example.py")
    print("3. Hướng dẫn chi tiết: Xem file USAGE_GUIDE.md")
    
    # Uncomment dòng dưới để chạy Gradio interface
    # interface = app.create_gradio_interface()
    # interface.launch(share=True)

if __name__ == "__main__":
    main()
