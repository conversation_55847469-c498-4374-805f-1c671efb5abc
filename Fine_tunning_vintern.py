from datasets import load_dataset, Dataset
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    Trainer,
    TrainingArguments,
    DataCollatorWithPadding
)
from peft import LoraConfig, get_peft_model, TaskType
from torchvision import transforms
import torch
from huggingface_hub import login
import os
import gc
import shutil
from sklearn.metrics import accuracy_score, f1_score
import matplotlib.pyplot as plt
from PIL import Image
import numpy as np
from torch.utils.data import DataLoader
import warnings
warnings.filterwarnings("ignore")

# 0. Cấu hình môi trường và đăng nhập
print("🚀 Bắt đầu fine-tuning Vintern với dataset Viet-OCR-VQA...")

# Đăng nhập HuggingFace
login(token="*************************************")

# Tạo thư mục cache tạm thời và dọn dẹp nếu cần
cache_dir = "D:/THUC TAP/Finr_Tunning_Vintern/cache"
temp_cache = "D:/THUC TAP/Finr_Tunning_Vintern/temp_cache"

# Dọn dẹp cache cũ để giải phóng dung lượng
if os.path.exists(cache_dir):
    print("🧹 Dọn dẹp cache cũ...")
    shutil.rmtree(cache_dir, ignore_errors=True)

os.makedirs(cache_dir, exist_ok=True)
os.makedirs(temp_cache, exist_ok=True)

os.environ["HF_DATASETS_CACHE"] = cache_dir
os.environ["HF_HOME"] = temp_cache
os.environ["TRANSFORMERS_CACHE"] = temp_cache

# 1. Tạo dataset demo nhỏ để test
print("📥 Tạo dataset demo nhỏ để test fine-tuning...")

# Tạo dataset demo với dữ liệu giả lập
demo_data = []
for i in range(50):  # Chỉ 50 samples để test nhanh
    # Tạo ảnh RGB đơn giản
    from PIL import Image
    import numpy as np

    # Tạo ảnh màu ngẫu nhiên 224x224
    img_array = np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8)
    img = Image.fromarray(img_array)

    # Tạo conversation đơn giản
    questions = [
        "Có gì trong ảnh này?",
        "Màu sắc chủ đạo là gì?",
        "Mô tả ảnh này",
        "Bạn thấy gì trong hình?",
        "Ảnh này về chủ đề gì?"
    ]

    answers = [
        "Đây là một hình ảnh màu sắc.",
        "Ảnh có nhiều màu khác nhau.",
        "Tôi thấy một hình ảnh đầy màu sắc.",
        "Đây là một bức ảnh đẹp.",
        "Ảnh này rất thú vị."
    ]

    question = questions[i % len(questions)]
    answer = answers[i % len(answers)]

    demo_data.append({
        "image": img,
        "conversations": [
            {"role": "user", "content": question},
            {"role": "assistant", "content": answer}
        ]
    })

# Tạo dataset từ dữ liệu demo
train_dataset = Dataset.from_list(demo_data[:40])  # 40 samples cho train
test_dataset = Dataset.from_list(demo_data[40:])   # 10 samples cho test

print(f"✅ Đã tạo {len(train_dataset)} samples train và {len(test_dataset)} samples test")
print("Example keys:", train_dataset[0].keys())

# Nếu muốn dùng dataset thật, uncomment dòng dưới (nhưng sẽ mất nhiều thời gian)
# dataset = load_dataset("5CD-AI/Viet-OCR-VQA")
# train_dataset = dataset["train"].select(range(100))  # Chỉ lấy 100 samples
# test_dataset = dataset["test"].select(range(20))     # Chỉ lấy 20 samples

# 2. Tạo tokenizer & model với tối ưu bộ nhớ
print("🤖 Đang tải tokenizer và model...")
try:
    tok = AutoTokenizer.from_pretrained(
        "5CD-AI/Vintern-1B-v3_5",
        trust_remote_code=True,
        cache_dir=temp_cache
    )

    # Thêm pad token nếu chưa có
    if tok.pad_token is None:
        tok.pad_token = tok.eos_token

    model = AutoModelForCausalLM.from_pretrained(
        "5CD-AI/Vintern-1B-v3_5",
        trust_remote_code=True,
        torch_dtype=torch.float16,  # Sử dụng float16 để tiết kiệm bộ nhớ
        device_map="cpu",  # Sử dụng CPU để tránh lỗi bộ nhớ
        low_cpu_mem_usage=True,
        cache_dir=temp_cache
    )
    print("✅ Đã tải model thành công")

except Exception as e:
    print(f"❌ Lỗi khi tải model: {e}")
    exit(1)

# 3. Xây dựng transform cho ảnh (tối ưu hóa)
image_transform = transforms.Compose([
    transforms.Resize((224, 224)),  # Giảm kích thước để tiết kiệm bộ nhớ
    transforms.ToTensor(),
    transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),  # ImageNet normalization
])

# 4. Hàm tiền xử lý tối ưu
def preprocess(example):
    try:
        # 4.1 xử lý ảnh
        img = example["image"]
        if isinstance(img, str):
            img = Image.open(img).convert("RGB")
        elif not isinstance(img, Image.Image):
            img = Image.fromarray(img).convert("RGB")

        pixel_values = image_transform(img)

        # 4.2 lấy câu hỏi và câu trả lời
        conv = example["conversations"]
        question = conv[0]["content"]
        answer = conv[1]["content"]

        # 4.3 tạo prompt theo format của Vintern
        prompt = f"<|im_start|>user\n{question}<|im_end|>\n<|im_start|>assistant\n"
        full_text = prompt + answer + "<|im_end|>"

        # 4.4 tokenize
        tokenized = tok(
            full_text,
            max_length=512,  # Giới hạn độ dài để tiết kiệm bộ nhớ
            padding="max_length",
            truncation=True,
            return_tensors="pt"
        )

        input_ids = tokenized.input_ids.squeeze()
        attention_mask = tokenized.attention_mask.squeeze()

        # Labels = input_ids, nhưng mask phần prompt
        labels = input_ids.clone()
        prompt_tokens = tok(prompt, add_special_tokens=False)["input_ids"]
        labels[:len(prompt_tokens)] = -100  # Ignore prompt trong loss

        return {
            "pixel_values": pixel_values,
            "input_ids": input_ids,
            "attention_mask": attention_mask,
            "labels": labels,
        }
    except Exception as e:
        print(f"Lỗi xử lý sample: {e}")
        return None

# 5. Áp dụng tiền xử lý với xử lý lỗi
print("🔄 Đang tiền xử lý dữ liệu...")
try:
    # Xử lý train dataset
    train_ds = train_dataset.map(
        preprocess,
        remove_columns=train_dataset.column_names,
        batched=False,
        num_proc=1  # Sử dụng 1 process để tránh lỗi bộ nhớ
    )

    # Lọc bỏ các samples None
    train_ds = train_ds.filter(lambda x: x is not None)

    # Xử lý test dataset
    test_ds = test_dataset.map(
        preprocess,
        remove_columns=test_dataset.column_names,
        batched=False,
        num_proc=1
    )
    test_ds = test_ds.filter(lambda x: x is not None)

    print(f"✅ Đã xử lý {len(train_ds)} samples train và {len(test_ds)} samples test")

except Exception as e:
    print(f"❌ Lỗi khi tiền xử lý: {e}")
    exit(1)

# 6. LoRA setup tối ưu
print("⚙️ Cấu hình LoRA...")
lora_cfg = LoraConfig(
    r=16,  # Tăng rank để cải thiện hiệu suất
    lora_alpha=32,
    target_modules=["q_proj", "v_proj", "k_proj", "o_proj"],  # Thêm nhiều modules
    lora_dropout=0.1,
    bias="none",
    task_type="CAUSAL_LM"
)

model = get_peft_model(model, lora_cfg)
model.print_trainable_parameters()

# 7. TrainingArguments tối ưu
print("📋 Cấu hình training arguments...")
training_args = TrainingArguments(
    output_dir="./finetuned-vintern-ocr-vqa",
    per_device_train_batch_size=1,  # Giảm xuống 1 để tiết kiệm bộ nhớ tối đa
    gradient_accumulation_steps=8,  # Giảm để tránh quá tải bộ nhớ
    learning_rate=1e-4,  # Giảm learning rate
    num_train_epochs=1,  # Chỉ 1 epoch để test nhanh
    fp16=False,  # Tắt fp16 để tránh lỗi
    logging_steps=5,
    save_total_limit=1,  # Chỉ giữ 1 checkpoint
    evaluation_strategy="no",
    save_steps=50,
    save_strategy="steps",
    load_best_model_at_end=False,
    dataloader_drop_last=True,
    remove_unused_columns=False,
    warmup_steps=10,
    weight_decay=0.01,
    max_grad_norm=1.0,
    report_to=None,  # Tắt wandb/tensorboard
    use_cpu=True,  # Sử dụng CPU
)

# 8. Data collator tùy chỉnh
class CustomDataCollator:
    def __init__(self, tokenizer):
        self.tokenizer = tokenizer

    def __call__(self, features):
        batch = {}

        # Xử lý pixel_values
        if "pixel_values" in features[0]:
            batch["pixel_values"] = torch.stack([f["pixel_values"] for f in features])

        # Xử lý text inputs
        input_ids = [f["input_ids"] for f in features]
        attention_mask = [f["attention_mask"] for f in features]
        labels = [f["labels"] for f in features]

        # Padding
        max_len = max(len(ids) for ids in input_ids)

        padded_input_ids = []
        padded_attention_mask = []
        padded_labels = []

        for i in range(len(input_ids)):
            pad_len = max_len - len(input_ids[i])

            padded_input_ids.append(
                torch.cat([input_ids[i], torch.full((pad_len,), self.tokenizer.pad_token_id)])
            )
            padded_attention_mask.append(
                torch.cat([attention_mask[i], torch.zeros(pad_len)])
            )
            padded_labels.append(
                torch.cat([labels[i], torch.full((pad_len,), -100)])
            )

        batch["input_ids"] = torch.stack(padded_input_ids)
        batch["attention_mask"] = torch.stack(padded_attention_mask)
        batch["labels"] = torch.stack(padded_labels)

        return batch

data_collator = CustomDataCollator(tok)

# 9. Trainer với xử lý lỗi
print("🏋️ Khởi tạo Trainer...")
try:
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_ds,
        data_collator=data_collator,
        tokenizer=tok,
    )
    print("✅ Đã khởi tạo Trainer thành công")
except Exception as e:
    print(f"❌ Lỗi khi khởi tạo Trainer: {e}")
    exit(1)

# 10. Fine-tune với xử lý lỗi
print("🚀 Bắt đầu fine-tuning...")
try:
    trainer.train()
    print("✅ Fine-tuning hoàn thành!")

    # Lưu model
    output_dir = "./finetuned-vintern-ocr-vqa"
    trainer.save_model(output_dir)
    tok.save_pretrained(output_dir)
    print(f"💾 Đã lưu model tại: {output_dir}")

except Exception as e:
    print(f"❌ Lỗi trong quá trình training: {e}")
    print("💡 Thử giảm batch_size hoặc tăng gradient_accumulation_steps")

# 11. Đánh giá model
print("📊 Đánh giá model...")
try:
    # Đánh giá trên test set
    eval_results = trainer.evaluate(eval_dataset=test_ds)
    print("Kết quả đánh giá:", eval_results)

    # Test inference với một vài samples
    print("\n🔍 Test inference:")
    model.eval()

    for i in range(min(3, len(test_ds))):
        sample = test_ds[i]

        # Chuẩn bị input
        inputs = {
            "input_ids": sample["input_ids"].unsqueeze(0),
            "attention_mask": sample["attention_mask"].unsqueeze(0),
            "pixel_values": sample["pixel_values"].unsqueeze(0)
        }

        # Generate
        with torch.no_grad():
            outputs = model.generate(
                **inputs,
                max_new_tokens=100,
                do_sample=True,
                temperature=0.7,
                pad_token_id=tok.pad_token_id
            )

        # Decode
        generated_text = tok.decode(outputs[0], skip_special_tokens=True)
        print(f"\nSample {i+1}:")
        print(f"Generated: {generated_text}")

except Exception as e:
    print(f"❌ Lỗi khi đánh giá: {e}")

# 12. Dọn dẹp
print("🧹 Dọn dẹp bộ nhớ...")
try:
    import gc
    torch.cuda.empty_cache()
    gc.collect()

    # Dọn dẹp cache tạm thời
    if os.path.exists(temp_cache):
        shutil.rmtree(temp_cache, ignore_errors=True)

    print("✅ Hoàn thành!")

except Exception as e:
    print(f"⚠️ Lỗi khi dọn dẹp: {e}")

print("\n🎉 Fine-tuning Vintern hoàn thành!")
print(f"📁 Model đã được lưu tại: ./finetuned-vintern-ocr-vqa")
print("💡 Bạn có thể sử dụng model này để inference với OCR-VQA tasks")
