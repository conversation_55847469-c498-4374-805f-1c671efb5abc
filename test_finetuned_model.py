"""
Script để test model Vintern đã được fine-tune với dataset Viet-OCR-VQA
"""

import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from PIL import Image
from torchvision import transforms
import os

def load_finetuned_model(model_path="./finetuned-vintern-ocr-vqa"):
    """Load model đã được fine-tune"""
    print(f"🤖 Đang tải model từ {model_path}...")
    
    try:
        tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
        model = AutoModelForCausalLM.from_pretrained(
            model_path, 
            trust_remote_code=True,
            torch_dtype=torch.float16,
            device_map="auto"
        )
        
        if tokenizer.pad_token is None:
            tokenizer.pad_token = tokenizer.eos_token
            
        print("✅ Đã tải model thành công!")
        return tokenizer, model
        
    except Exception as e:
        print(f"❌ Lỗi khi tải model: {e}")
        print("💡 <PERSON><PERSON><PERSON> đảm bảo đã chạy fine-tuning thành công trước")
        return None, None

def preprocess_image(image_path):
    """Tiền xử lý ảnh"""
    transform = transforms.Compose([
        transforms.Resize((224, 224)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    try:
        image = Image.open(image_path).convert("RGB")
        pixel_values = transform(image).unsqueeze(0)
        return pixel_values
    except Exception as e:
        print(f"❌ Lỗi khi xử lý ảnh: {e}")
        return None

def generate_answer(tokenizer, model, image_path, question):
    """Generate câu trả lời cho câu hỏi về ảnh"""
    
    # Tiền xử lý ảnh
    pixel_values = preprocess_image(image_path)
    if pixel_values is None:
        return "Lỗi xử lý ảnh"
    
    # Tạo prompt
    prompt = f"<|im_start|>user\n{question}<|im_end|>\n<|im_start|>assistant\n"
    
    # Tokenize
    inputs = tokenizer(
        prompt,
        return_tensors="pt",
        max_length=512,
        truncation=True
    )
    
    try:
        # Generate
        with torch.no_grad():
            outputs = model.generate(
                input_ids=inputs["input_ids"],
                attention_mask=inputs["attention_mask"],
                pixel_values=pixel_values,
                max_new_tokens=100,
                do_sample=True,
                temperature=0.7,
                top_p=0.9,
                pad_token_id=tokenizer.pad_token_id,
                eos_token_id=tokenizer.eos_token_id
            )
        
        # Decode
        generated_text = tokenizer.decode(outputs[0], skip_special_tokens=True)
        
        # Lấy phần answer
        if "<|im_start|>assistant\n" in generated_text:
            answer = generated_text.split("<|im_start|>assistant\n")[-1]
            if "<|im_end|>" in answer:
                answer = answer.split("<|im_end|>")[0]
            return answer.strip()
        else:
            return generated_text.strip()
            
    except Exception as e:
        print(f"❌ Lỗi khi generate: {e}")
        return "Lỗi generate"

def main():
    """Hàm chính để test model"""
    print("🧪 Test Fine-tuned Vintern Model")
    print("=" * 50)
    
    # Load model
    tokenizer, model = load_finetuned_model()
    if tokenizer is None or model is None:
        return
    
    # Test với ảnh mẫu
    print("\n📝 Nhập thông tin để test:")
    
    while True:
        image_path = input("\n📷 Đường dẫn ảnh (hoặc 'quit' để thoát): ").strip()
        
        if image_path.lower() == 'quit':
            break
            
        if not os.path.exists(image_path):
            print("❌ File ảnh không tồn tại!")
            continue
            
        question = input("❓ Câu hỏi về ảnh: ").strip()
        
        if not question:
            print("❌ Vui lòng nhập câu hỏi!")
            continue
        
        print("\n🤔 Đang suy nghĩ...")
        answer = generate_answer(tokenizer, model, image_path, question)
        
        print(f"\n💬 Câu trả lời: {answer}")
        print("-" * 50)

if __name__ == "__main__":
    main()
