# Fine-tuning Vintern với Dataset Viet-OCR-VQA

Dự án này thực hiện fine-tuning mô hình Vintern-1B-v3_5 với dataset Viet-OCR-VQA để cải thiện khả năng trả lời câu hỏi về hình ảnh bằng tiếng Việt.

## 🚀 Tính năng

- ✅ Fine-tuning mô hình Vintern với LoRA (Low-Rank Adaptation)
- ✅ Xử lý dataset Viet-OCR-VQA với streaming để tiết kiệm bộ nhớ
- ✅ Tối ưu hóa bộ nhớ và dung lượng ổ đĩa
- ✅ Script demo với model nhỏ để test
- ✅ Kiểm tra và dọn dẹp cache tự động
- ✅ Test inference sau khi fine-tuning

## 📋 Yêu cầu hệ thống

### Tối thiểu (cho demo):
- RAM: 8GB+
- Dung lượng ổ đĩa: 10GB trống
- Python 3.8+
- CPU: Intel i5 hoặc tương đương

### Khuyến nghị (cho fine-tuning thật):
- RAM: 32GB+
- GPU: RTX 3080/4080 với 12GB+ VRAM
- Dung lượng ổ đĩa: 50GB+ trống
- Python 3.9+

## 🛠️ Cài đặt

1. **Clone repository:**
```bash
git clone <repository-url>
cd Finr_Tunning_Vintern
```

2. **Cài đặt dependencies:**
```bash
pip install -r requirements.txt
```

3. **Thiết lập HuggingFace token:**
   - Đăng ký tài khoản tại [HuggingFace](https://huggingface.co/)
   - Tạo access token với quyền read
   - Thay thế token trong file script

## 📁 Cấu trúc dự án

```
Finr_Tunning_Vintern/
├── Fine_tunning_vintern.py      # Script chính fine-tuning Vintern
├── Fine_tuning_demo.py          # Demo với model nhỏ
├── test_finetuned_model.py      # Test model sau fine-tuning
├── check_disk_space.py          # Kiểm tra và dọn dẹp ổ đĩa
├── run_finetuning.py           # Script tổng hợp
├── requirements.txt            # Dependencies
└── README.md                   # Hướng dẫn này
```

## 🚀 Cách sử dụng

### Option 1: Chạy demo nhanh (khuyến nghị cho test)
```bash
python Fine_tuning_demo.py
```

### Option 2: Fine-tuning Vintern thật
```bash
# Kiểm tra dung lượng ổ đĩa trước
python check_disk_space.py

# Chạy fine-tuning
python Fine_tunning_vintern.py

# Hoặc chạy script tổng hợp
python run_finetuning.py
```

### Option 3: Test model đã fine-tune
```bash
python test_finetuned_model.py
```

## ⚙️ Cấu hình

### Tối ưu cho hệ thống yếu:
- Giảm `per_device_train_batch_size` xuống 1
- Tăng `gradient_accumulation_steps`
- Sử dụng `device_map="cpu"`
- Giảm `max_length` trong tokenization
- Sử dụng dataset nhỏ hơn

### Tối ưu cho hệ thống mạnh:
- Tăng `per_device_train_batch_size` lên 4-8
- Sử dụng `device_map="auto"`
- Bật `fp16=True`
- Tăng `num_train_epochs`

## 🔧 Khắc phục sự cố

### Lỗi "No space left on device":
```bash
python check_disk_space.py
# Chọn 'y' để dọn dẹp cache
```

### Lỗi "Out of memory":
- Giảm batch_size xuống 1
- Sử dụng CPU thay vì GPU
- Giảm max_length
- Sử dụng dataset nhỏ hơn

### Lỗi "Paging file too small":
- Tăng virtual memory trong Windows
- Đóng các ứng dụng khác
- Sử dụng demo script thay vì full script

## 📊 Kết quả mong đợi

### Demo script:
- Thời gian: 2-5 phút
- Model size: ~350MB
- Chỉ để test quy trình

### Fine-tuning thật:
- Thời gian: 2-6 giờ (tùy GPU)
- Model size: ~2GB
- Cải thiện khả năng VQA tiếng Việt

## 🎯 Tối ưu hóa đã thực hiện

1. **Tiết kiệm bộ nhớ:**
   - Sử dụng LoRA thay vì full fine-tuning
   - Streaming dataset
   - Gradient checkpointing
   - Float16 precision

2. **Tiết kiệm dung lượng:**
   - Tự động dọn dẹp cache
   - Giới hạn số checkpoint
   - Sử dụng subset dataset

3. **Tăng tốc độ:**
   - Batch processing
   - Gradient accumulation
   - Optimized data loading

## 📝 Ghi chú

- Script đã được tối ưu để chạy trên hệ thống có cấu hình thấp
- Để có kết quả tốt nhất, nên sử dụng GPU và dataset đầy đủ
- Model Vintern yêu cầu trust_remote_code=True
- Đảm bảo có kết nối internet ổn định khi tải model

## 🤝 Đóng góp

Mọi đóng góp và phản hồi đều được chào đón! Vui lòng tạo issue hoặc pull request.

## 📄 License

Dự án này tuân theo license của các model và dataset được sử dụng.
