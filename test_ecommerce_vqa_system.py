"""
Comprehensive testing script for E-commerce VQA Chatbot System
"""

import requests
import json
import time
import os
import base64
from PIL import Image
import io
import numpy as np
from datetime import datetime
import asyncio
import aiohttp

class EcommerceVQASystemTester:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.session_id = None
        self.test_results = []
        
    def create_test_image(self, text="Test Product", size=(224, 224)):
        """Tạo ảnh test đơn giản"""
        img = Image.new('RGB', size, color='lightblue')
        # Có thể thêm text vào ảnh nếu cần
        return img
    
    def image_to_bytes(self, image):
        """Chuyển PIL Image thành bytes"""
        img_byte_arr = io.BytesIO()
        image.save(img_byte_arr, format='PNG')
        return img_byte_arr.getvalue()
    
    def test_api_health(self):
        """Test API health check"""
        print("🏥 Testing API health...")
        
        try:
            response = requests.get(f"{self.base_url}/", timeout=10)
            if response.status_code == 200:
                print("✅ API is healthy")
                return True
            else:
                print(f"❌ API health check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ API health check error: {e}")
            return False
    
    def test_chat_endpoint(self):
        """Test chat endpoint với các scenarios khác nhau"""
        print("💬 Testing chat endpoint...")
        
        test_cases = [
            {
                "question": "Sản phẩm này có màu nào khác không?",
                "description": "Color inquiry",
                "has_image": True
            },
            {
                "question": "Giá của sản phẩm này là bao nhiêu?",
                "description": "Price inquiry",
                "has_image": True
            },
            {
                "question": "Chất liệu của sản phẩm là gì?",
                "description": "Material inquiry",
                "has_image": False
            },
            {
                "question": "Sản phẩm có còn hàng không?",
                "description": "Availability inquiry",
                "has_image": False
            },
            {
                "question": "Mô tả chi tiết sản phẩm này",
                "description": "General description",
                "has_image": True
            }
        ]
        
        results = []
        
        for i, test_case in enumerate(test_cases):
            print(f"  📝 Test {i+1}: {test_case['description']}")
            
            start_time = time.time()
            
            try:
                # Prepare request
                data = {
                    "question": test_case["question"]
                }
                
                if self.session_id:
                    data["session_id"] = self.session_id
                
                files = {}
                if test_case["has_image"]:
                    test_img = self.create_test_image(f"Product {i+1}")
                    img_bytes = self.image_to_bytes(test_img)
                    files["image"] = ("test_image.png", img_bytes, "image/png")
                
                # Send request
                response = requests.post(
                    f"{self.base_url}/api/chat",
                    data=data,
                    files=files,
                    timeout=30
                )
                
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    result = response.json()
                    
                    # Store session ID from first response
                    if not self.session_id:
                        self.session_id = result.get("session_id")
                    
                    test_result = {
                        "test_case": test_case["description"],
                        "question": test_case["question"],
                        "answer": result.get("answer", ""),
                        "confidence": result.get("confidence_score", 0),
                        "response_time": response_time,
                        "api_response_time": result.get("response_time", 0),
                        "suggested_questions": result.get("suggested_questions", []),
                        "status": "success"
                    }
                    
                    print(f"    ✅ Response: {result.get('answer', '')[:50]}...")
                    print(f"    📊 Confidence: {result.get('confidence_score', 0):.2f}")
                    print(f"    ⏱️ Time: {response_time:.2f}s")
                    
                else:
                    test_result = {
                        "test_case": test_case["description"],
                        "question": test_case["question"],
                        "error": f"HTTP {response.status_code}",
                        "response_time": response_time,
                        "status": "failed"
                    }
                    print(f"    ❌ Failed: HTTP {response.status_code}")
                
                results.append(test_result)
                
            except Exception as e:
                test_result = {
                    "test_case": test_case["description"],
                    "question": test_case["question"],
                    "error": str(e),
                    "response_time": time.time() - start_time,
                    "status": "error"
                }
                results.append(test_result)
                print(f"    ❌ Error: {e}")
            
            # Wait between requests
            time.sleep(1)
        
        return results
    
    def test_metrics_endpoint(self):
        """Test metrics endpoint"""
        print("📊 Testing metrics endpoint...")
        
        try:
            response = requests.get(f"{self.base_url}/api/metrics", timeout=10)
            
            if response.status_code == 200:
                metrics = response.json()
                print("✅ Metrics endpoint working")
                print(f"    📈 Total conversations: {metrics.get('total_conversations', 0)}")
                print(f"    ⏱️ Avg response time: {metrics.get('avg_response_time', 0):.3f}s")
                print(f"    🎯 Avg confidence: {metrics.get('avg_confidence', 0):.3f}")
                return metrics
            else:
                print(f"❌ Metrics endpoint failed: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Metrics endpoint error: {e}")
            return None
    
    def test_chat_history(self):
        """Test chat history endpoint"""
        print("📜 Testing chat history...")
        
        if not self.session_id:
            print("⚠️ No session ID available")
            return None
        
        try:
            response = requests.get(
                f"{self.base_url}/api/sessions/{self.session_id}/history",
                timeout=10
            )
            
            if response.status_code == 200:
                history = response.json()
                message_count = len(history.get("messages", []))
                print(f"✅ Chat history retrieved: {message_count} messages")
                return history
            else:
                print(f"❌ Chat history failed: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Chat history error: {e}")
            return None
    
    def test_product_endpoints(self):
        """Test e-commerce specific endpoints"""
        print("🛒 Testing product endpoints...")
        
        # Test product search
        try:
            response = requests.get(
                f"{self.base_url}/api/products/search",
                params={"query": "áo"},
                timeout=10
            )
            
            if response.status_code == 200:
                results = response.json()
                print(f"✅ Product search: {len(results.get('results', []))} products found")
            else:
                print(f"❌ Product search failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Product search error: {e}")
        
        # Test product details
        try:
            response = requests.get(f"{self.base_url}/api/products/1", timeout=10)
            
            if response.status_code == 200:
                product = response.json()
                print(f"✅ Product details: {product.get('name', 'Unknown')}")
            else:
                print(f"❌ Product details failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Product details error: {e}")
    
    def performance_test(self, num_requests=10):
        """Test hiệu suất với nhiều requests"""
        print(f"⚡ Performance testing with {num_requests} requests...")
        
        response_times = []
        success_count = 0
        
        for i in range(num_requests):
            start_time = time.time()
            
            try:
                response = requests.post(
                    f"{self.base_url}/api/chat",
                    data={"question": f"Test question {i+1}"},
                    timeout=30
                )
                
                response_time = time.time() - start_time
                response_times.append(response_time)
                
                if response.status_code == 200:
                    success_count += 1
                
            except Exception as e:
                print(f"Request {i+1} failed: {e}")
            
            # Small delay between requests
            time.sleep(0.1)
        
        if response_times:
            avg_time = np.mean(response_times)
            min_time = np.min(response_times)
            max_time = np.max(response_times)
            success_rate = (success_count / num_requests) * 100
            
            print(f"📊 Performance Results:")
            print(f"    ✅ Success rate: {success_rate:.1f}%")
            print(f"    ⏱️ Avg response time: {avg_time:.3f}s")
            print(f"    🚀 Min response time: {min_time:.3f}s")
            print(f"    🐌 Max response time: {max_time:.3f}s")
            
            return {
                "success_rate": success_rate,
                "avg_response_time": avg_time,
                "min_response_time": min_time,
                "max_response_time": max_time,
                "total_requests": num_requests
            }
        
        return None
    
    def generate_test_report(self):
        """Tạo báo cáo test chi tiết"""
        print("📄 Generating test report...")
        
        report = {
            "test_timestamp": datetime.now().isoformat(),
            "base_url": self.base_url,
            "session_id": self.session_id,
            "test_results": self.test_results,
            "summary": {
                "total_tests": len(self.test_results),
                "successful_tests": len([r for r in self.test_results if r.get("status") == "success"]),
                "failed_tests": len([r for r in self.test_results if r.get("status") != "success"])
            }
        }
        
        # Save report
        with open("ecommerce_vqa_test_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print("✅ Test report saved to: ecommerce_vqa_test_report.json")
        return report
    
    def run_comprehensive_test(self):
        """Chạy test toàn diện"""
        print("🧪 Running Comprehensive E-commerce VQA System Test")
        print("=" * 60)
        
        # 1. Health check
        if not self.test_api_health():
            print("❌ API health check failed. Stopping tests.")
            return False
        
        # 2. Chat endpoint tests
        chat_results = self.test_chat_endpoint()
        self.test_results.extend(chat_results)
        
        # 3. Metrics test
        metrics = self.test_metrics_endpoint()
        
        # 4. Chat history test
        history = self.test_chat_history()
        
        # 5. Product endpoints test
        self.test_product_endpoints()
        
        # 6. Performance test
        perf_results = self.performance_test(num_requests=5)
        
        # 7. Generate report
        report = self.generate_test_report()
        
        # Summary
        print("\n🎉 Test Summary")
        print("-" * 30)
        print(f"✅ Successful tests: {report['summary']['successful_tests']}")
        print(f"❌ Failed tests: {report['summary']['failed_tests']}")
        print(f"📊 Success rate: {(report['summary']['successful_tests'] / report['summary']['total_tests'] * 100):.1f}%")
        
        if perf_results:
            print(f"⚡ Avg response time: {perf_results['avg_response_time']:.3f}s")
            print(f"🎯 Performance success rate: {perf_results['success_rate']:.1f}%")
        
        return True

def main():
    """Main testing function"""
    print("🧪 E-commerce VQA System Tester")
    print("=" * 40)
    
    # Check if server is running
    tester = EcommerceVQASystemTester()
    
    if not tester.test_api_health():
        print("\n💡 Make sure the server is running:")
        print("   python ecommerce_vqa_backend.py")
        return
    
    # Run comprehensive tests
    success = tester.run_comprehensive_test()
    
    if success:
        print("\n✅ All tests completed successfully!")
    else:
        print("\n❌ Some tests failed. Check the report for details.")

if __name__ == "__main__":
    main()
