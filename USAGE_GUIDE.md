
# 📖 HƯỚNG DẪN SỬ DỤNG MODEL VINTERN VQA

## 🎯 Mục đích và Ứng dụng

### Model này có thể được sử dụng cho:

1. **Hệ thống hỏi đáp về ảnh:**
   - Chatbot hỗ trợ khách hàng với ảnh
   - Ứng dụng giáo dục tương tác
   - Công cụ hỗ trợ người khiếm thị

2. **Phân tích nội dung ảnh:**
   - M<PERSON> tả tự động ảnh cho website
   - Kiểm duyệt nội dung hình ảnh
   - Tạo caption cho social media

3. **Ứng dụng thương mại:**
   - E-commerce: mô tả sản phẩm
   - Du lịch: giới thiệu địa điểm
   - Giáo dục: giải thích hình ảnh

## 🚀 Cách sử dụng

### 1. Sử dụng qua Gradio Interface:
```bash
python application_guide.py
```

### 2. Sử dụng qua API:
```python
# Xem file api_example.py
```

### 3. T<PERSON>ch hợp vào ứng dụng:
```python
from vintern_vqa import VinternVQA

vqa = VinternVQA("./finetuned-vintern-ocr-vqa")
answer = vqa.ask("Có gì trong ảnh này?")
```

## ⚠️ Lưu ý quan trọng

### Về hiệu suất:
- Model hoạt động tốt nhất với GPU
- Thời gian response: 1-3 giây/câu hỏi
- RAM yêu cầu: 4-8GB

### Về chất lượng:
- Độ chính xác: 60-80% (tùy loại câu hỏi)
- Tốt nhất với câu hỏi mô tả chung
- Hạn chế với câu hỏi chi tiết phức tạp

### Về ngôn ngữ:
- Chủ yếu hỗ trợ tiếng Việt
- Có thể hiểu một số tiếng Anh đơn giản
- Câu trả lời luôn bằng tiếng Việt

## 🔧 Tùy chỉnh

### Điều chỉnh tham số generation:
```python
outputs = model.generate(
    **inputs,
    max_new_tokens=150,      # Độ dài câu trả lời
    temperature=0.7,         # Tính sáng tạo (0.1-1.0)
    top_p=0.9,              # Đa dạng từ vựng
    do_sample=True          # Bật sampling
)
```

### Tối ưu hiệu suất:
```python
# Sử dụng float16
model = AutoModelForCausalLM.from_pretrained(
    model_path,
    torch_dtype=torch.float16
)

# Sử dụng CPU nếu không có GPU
model = AutoModelForCausalLM.from_pretrained(
    model_path,
    device_map="cpu"
)
```

## 📊 Đánh giá chất lượng

Chạy script đánh giá:
```bash
python evaluate_model.py
```

Kết quả mong đợi:
- Điểm chất lượng: 0.6-0.8/1.0
- Thời gian response: 1-3s
- Độ chính xác theo category khác nhau

## 🐛 Khắc phục sự cố

### Model không load được:
- Kiểm tra đường dẫn model
- Đảm bảo đủ RAM/VRAM
- Thử sử dụng CPU mode

### Câu trả lời không chính xác:
- Điều chỉnh temperature
- Thử câu hỏi rõ ràng hơn
- Kiểm tra chất lượng ảnh input

### Chậm khi inference:
- Sử dụng GPU nếu có
- Giảm max_new_tokens
- Tắt do_sample nếu không cần

## 📈 Cải thiện model

### Fine-tune thêm:
- Thêm dữ liệu domain-specific
- Tăng số epoch training
- Điều chỉnh learning rate

### Tối ưu cho use case cụ thể:
- Tạo prompt template phù hợp
- Filter câu hỏi theo category
- Post-process câu trả lời
