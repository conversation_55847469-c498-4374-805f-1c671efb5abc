"""
Script để kiểm tra dung lượng ổ đĩa và dọn dẹp cache
"""

import os
import shutil
import psutil

def get_disk_usage(path):
    """Lấy thông tin sử dụng ổ đĩa"""
    try:
        usage = psutil.disk_usage(path)
        total = usage.total / (1024**3)  # GB
        used = usage.used / (1024**3)   # GB
        free = usage.free / (1024**3)   # GB
        percent = (used / total) * 100
        
        return {
            'total': total,
            'used': used,
            'free': free,
            'percent': percent
        }
    except Exception as e:
        print(f"Lỗi khi kiểm tra ổ đĩa: {e}")
        return None

def get_folder_size(path):
    """Tính kích thước thư mục"""
    total_size = 0
    try:
        for dirpath, dirnames, filenames in os.walk(path):
            for filename in filenames:
                filepath = os.path.join(dirpath, filename)
                if os.path.exists(filepath):
                    total_size += os.path.getsize(filepath)
    except Exception as e:
        print(f"Lỗi khi tính kích thước thư mục {path}: {e}")
    
    return total_size / (1024**3)  # GB

def clean_cache_directories():
    """Dọn dẹp các thư mục cache"""
    cache_dirs = [
        "D:/THUC TAP/Finr_Tunning_Vintern/cache",
        "D:/THUC TAP/Finr_Tunning_Vintern/temp_cache",
        os.path.expanduser("~/.cache/huggingface"),
        os.path.expanduser("~/.cache/torch"),
    ]
    
    total_freed = 0
    
    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir):
            try:
                size_before = get_folder_size(cache_dir)
                shutil.rmtree(cache_dir, ignore_errors=True)
                total_freed += size_before
                print(f"✅ Đã xóa {cache_dir} ({size_before:.2f} GB)")
            except Exception as e:
                print(f"❌ Lỗi khi xóa {cache_dir}: {e}")
        else:
            print(f"ℹ️ {cache_dir} không tồn tại")
    
    return total_freed

def clean_temp_files():
    """Dọn dẹp file tạm thời"""
    temp_patterns = [
        "*.tmp",
        "*.temp",
        "*~",
        ".DS_Store",
        "Thumbs.db"
    ]
    
    workspace = "D:/THUC TAP/Finr_Tunning_Vintern"
    total_freed = 0
    
    try:
        for root, dirs, files in os.walk(workspace):
            for file in files:
                if any(file.endswith(pattern.replace("*", "")) for pattern in temp_patterns):
                    filepath = os.path.join(root, file)
                    try:
                        size = os.path.getsize(filepath) / (1024**3)
                        os.remove(filepath)
                        total_freed += size
                        print(f"🗑️ Đã xóa {filepath}")
                    except Exception as e:
                        print(f"❌ Lỗi khi xóa {filepath}: {e}")
    except Exception as e:
        print(f"❌ Lỗi khi dọn dẹp temp files: {e}")
    
    return total_freed

def main():
    """Hàm chính"""
    print("💾 Kiểm tra dung lượng ổ đĩa và dọn dẹp")
    print("=" * 50)
    
    # Kiểm tra dung lượng ổ D
    print("\n📊 Thông tin ổ đĩa D:")
    disk_info = get_disk_usage("D:/")
    if disk_info:
        print(f"   Tổng dung lượng: {disk_info['total']:.2f} GB")
        print(f"   Đã sử dụng: {disk_info['used']:.2f} GB ({disk_info['percent']:.1f}%)")
        print(f"   Còn trống: {disk_info['free']:.2f} GB")
        
        if disk_info['free'] < 5:
            print("⚠️ CẢNH BÁO: Ổ đĩa sắp hết dung lượng!")
        elif disk_info['free'] < 10:
            print("⚠️ Ổ đĩa có ít dung lượng trống")
    
    # Kiểm tra kích thước workspace
    workspace = "D:/THUC TAP/Finr_Tunning_Vintern"
    print(f"\n📁 Kích thước workspace ({workspace}):")
    workspace_size = get_folder_size(workspace)
    print(f"   {workspace_size:.2f} GB")
    
    # Kiểm tra các thư mục cache
    print("\n🗂️ Kích thước các thư mục cache:")
    cache_dirs = [
        "D:/THUC TAP/Finr_Tunning_Vintern/cache",
        "D:/THUC TAP/Finr_Tunning_Vintern/temp_cache",
        os.path.expanduser("~/.cache/huggingface"),
        os.path.expanduser("~/.cache/torch"),
    ]
    
    total_cache_size = 0
    for cache_dir in cache_dirs:
        if os.path.exists(cache_dir):
            size = get_folder_size(cache_dir)
            total_cache_size += size
            print(f"   {cache_dir}: {size:.2f} GB")
        else:
            print(f"   {cache_dir}: Không tồn tại")
    
    print(f"\n📊 Tổng kích thước cache: {total_cache_size:.2f} GB")
    
    # Hỏi có muốn dọn dẹp không
    if total_cache_size > 0:
        response = input("\n🧹 Bạn có muốn dọn dẹp cache? (y/n): ").strip().lower()
        
        if response == 'y':
            print("\n🧹 Đang dọn dẹp...")
            
            # Dọn dẹp cache
            freed_cache = clean_cache_directories()
            
            # Dọn dẹp temp files
            freed_temp = clean_temp_files()
            
            total_freed = freed_cache + freed_temp
            print(f"\n✅ Đã giải phóng {total_freed:.2f} GB")
            
            # Kiểm tra lại dung lượng
            print("\n📊 Thông tin ổ đĩa sau khi dọn dẹp:")
            disk_info_after = get_disk_usage("D:/")
            if disk_info_after:
                print(f"   Còn trống: {disk_info_after['free']:.2f} GB")
        else:
            print("ℹ️ Bỏ qua dọn dẹp")
    
    print("\n💡 Gợi ý để tiết kiệm dung lượng:")
    print("   - Sử dụng streaming=True khi load dataset")
    print("   - Giảm batch_size trong training")
    print("   - Sử dụng gradient_checkpointing")
    print("   - Xóa các checkpoint cũ không cần thiết")
    print("   - Sử dụng float16 thay vì float32")

if __name__ == "__main__":
    main()
